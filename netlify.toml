[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NPM_FLAGS = "--legacy-peer-deps"
  DISABLE_ESLINT = "true"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.openai.com https://generativelanguage.googleapis.com https://www.googleapis.com https://accounts.google.com https://firebase.googleapis.com https://www.google-analytics.com https://identitytoolkit.googleapis.com https://firebaseinstallations.googleapis.com https://firebaseremoteconfig.googleapis.com; frame-src 'self' https://www.youtube.com https://accounts.google.com; object-src 'none'; base-uri 'self'; form-action 'self'"

# Ensure Next.js serverless function handles all routes (including API)
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/server"
  status = 200

[[redirects]]
  from = "/*"
  to = "/.netlify/functions/server"
  status = 200

