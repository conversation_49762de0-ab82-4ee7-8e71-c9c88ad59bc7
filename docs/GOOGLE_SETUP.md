# Google Cloud Console Setup

## 1. Create/Select Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing one
3. Enable billing (required for YouTube Analytics API)

## 2. Enable APIs
1. Go to "APIs & Services" > "Library"
2. Search and enable:
   - YouTube Data API v3
   - YouTube Analytics API
   - YouTube Reporting API

## 3. Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client ID"
3. Application type: "Web application"
4. Name: "Ytuber Analytics"
5. Authorized redirect URIs:
   - http://localhost:3000/api/auth/callback/google (development)
   - https://yourdomain.com/api/auth/callback/google (production)

## 4. Required <PERSON>opes
```
https://www.googleapis.com/auth/youtube.readonly
https://www.googleapis.com/auth/yt-analytics.readonly
https://www.googleapis.com/auth/youtube.channel-memberships.creator
```

## 5. Environment Variables
Add to .env.local:
```
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
YOUTUBE_API_KEY=your-api-key
```
