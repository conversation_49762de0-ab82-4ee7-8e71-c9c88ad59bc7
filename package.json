{"name": "ytuber", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "DISABLE_ESLINT=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "analyze": "ANALYZE=true npm run build", "cache:clear": "node -e \"console.log('Cache cleared')\"", "deploy:staging": "echo 'Deploying to staging...'", "deploy:production": "echo 'Deploying to production...'", "health-check": "curl -f http://localhost:3000/api/admin/health || exit 1", "security-audit": "npm audit --audit-level=high"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.84.2", "dayjs": "^1.11.13", "firebase": "^12.1.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.23.12", "googleapis": "^155.0.1", "iyzipay": "^2.0.64", "libphonenumber-js": "^1.12.13", "lucide-react": "^0.539.0", "next": "15.4.6", "openai": "^5.12.2", "react": "19.1.0", "react-dom": "19.1.0", "world-countries": "^5.1.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/iyzipay": "^2.0.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}