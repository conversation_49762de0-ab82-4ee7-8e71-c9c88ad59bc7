# 🚀 Ytuber - AI-Powered YouTube Growth Analysis

Modern, AI-powered YouTube channel analysis platform that helps creators grow responsibly and efficiently.

## ✨ Features

- **🔐 Firebase Authentication** - Secure login with Google via Firebase
- **🤖 AI-Powered Analysis** - Comprehensive channel insights using Gemini Vision and GPT-4
- **📊 Interactive Dashboard** - Beautiful, modern interface to view all your reports
- **📈 Detailed Reports** - SWOT analysis, video ideas, thumbnail tips, and 4-week action plans
- **📱 Fully Responsive** - Perfect experience on all devices
- **⚡ Fast & Efficient** - Quota-efficient API usage with smart caching

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Animations**: Framer Motion
- **Authentication**: Firebase Authentication with Google
- **Database**: Prisma with SQLite
- **Icons**: Lucide React
- **Styling**: Modern dark theme with gradients and animations

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- Firebase project with Authentication enabled

### Installation

1. Clone the repository:
```bash
git clone https://github.com/miktadtahir/ytuber.git
cd ytuber
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure your `.env.local`:
```env
# YouTube API
YOUTUBE_API_KEY=your-youtube-api-key

# Firebase (configured in lib/firebase/client.ts)
# No additional environment variables needed for Firebase Auth

# Other existing variables...
```

5. Set up the database:
```bash
npx prisma generate
npx prisma db push
```

6. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

## 🔧 Configuration

### Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Enable Authentication and add Google as a sign-in provider
4. Get your Firebase config and update `lib/firebase/client.ts`
5. For production, add your domain to authorized domains in Firebase Auth settings

## 📁 Project Structure

```
├── app/
│   ├── api/                 # API routes
│   ├── dashboard/           # Dashboard pages
│   ├── checkout/            # Payment flow
│   ├── providers/           # Context providers
│   └── globals.css          # Global styles
├── lib/
│   ├── auth.ts             # Firebase Auth utilities
│   ├── firebase/           # Firebase configuration
│   ├── prisma.ts           # Database client
│   └── youtube.ts          # YouTube API utilities
├── prisma/
│   └── schema.prisma       # Database schema
├── types/
│   └── next-auth.d.ts      # NextAuth type definitions
└── middleware.ts           # Route protection
```

## 🎨 Design Features

- **Modern Dark Theme** - Eye-friendly dark interface
- **Animated Backgrounds** - Floating blob animations
- **Micro-interactions** - Smooth hover and click effects
- **Responsive Design** - Mobile-first approach
- **Loading States** - Beautiful loading animations
- **Error Handling** - User-friendly error messages

## 🔒 Security

- All analysis endpoints protected by Firebase Authentication
- Token-based access control
- Secure API key management
- Firebase security rules and token verification

## 📊 Dashboard Features

- **Report Overview** - List all your channel analyses
- **Detailed Insights** - SWOT analysis with actionable recommendations
- **Video Ideas** - AI-generated content suggestions with hooks
- **Thumbnail Tips** - Optimization strategies with examples
- **Action Plans** - 4-week roadmap for growth
- **User Profile** - Account management and settings

## 🚀 Deployment

The app is ready for deployment on Vercel, Netlify, or any Node.js hosting platform.

```bash
npm run build
npm start
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
