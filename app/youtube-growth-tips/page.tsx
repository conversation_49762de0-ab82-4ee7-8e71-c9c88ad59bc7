import { Metadata } from 'next';
import Link from 'next/link';
import { Lightbulb, TrendingUp, Users, Target, BarChart3, Zap, CheckCircle, ArrowRight, Star } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Growth Tips | Proven Strategies to Grow Your Channel - YTuber',
  description: 'Discover proven YouTube growth tips and strategies. Learn how to increase subscribers, boost views, and grow your YouTube channel effectively in 2025.',
  keywords: 'YouTube growth tips, how to grow YouTube channel, YouTube growth strategies, increase YouTube subscribers, YouTube growth hacks, YouTube tips 2025',
  openGraph: {
    title: 'YouTube Growth Tips | Proven Strategies to Grow Your Channel - YTuber',
    description: 'Discover proven YouTube growth tips and strategies. Learn how to increase subscribers, boost views, and grow your YouTube channel effectively in 2025.',
    url: 'https://ytuber.life/youtube-growth-tips',
    type: 'website',
  },
  other: {
    canonical: 'https://ytuber.life/youtube-growth-tips',
  },
};

const growthTips = [
  {
    icon: Target,
    title: "Optimize Your Channel",
    tips: [
      {
        tip: "Create a compelling channel banner",
        description: "Design a professional banner that clearly communicates what your channel is about and when you upload."
      },
      {
        tip: "Write a keyword-rich channel description",
        description: "Include relevant keywords naturally in your channel description to help with discoverability."
      },
      {
        tip: "Organize content into playlists",
        description: "Group related videos into playlists to increase session duration and help viewers find more content."
      },
      {
        tip: "Add channel keywords",
        description: "Use relevant keywords in your channel settings to help YouTube understand your content niche."
      }
    ]
  },
  {
    icon: TrendingUp,
    title: "Content Strategy",
    tips: [
      {
        tip: "Research trending topics in your niche",
        description: "Use tools like Google Trends and YouTube search suggestions to find popular topics to cover."
      },
      {
        tip: "Create evergreen content",
        description: "Produce videos that will remain relevant and valuable to viewers months or years after publication."
      },
      {
        tip: "Develop a consistent posting schedule",
        description: "Regular uploads help build audience expectations and improve your channel's algorithmic performance."
      },
      {
        tip: "Create series and multi-part content",
        description: "Series encourage viewers to return and can significantly boost your overall watch time."
      }
    ]
  },
  {
    icon: Users,
    title: "Audience Engagement",
    tips: [
      {
        tip: "Respond to comments quickly",
        description: "Engage with your audience within the first few hours of posting to boost early engagement signals."
      },
      {
        tip: "Ask questions in your videos",
        description: "Encourage comments by asking specific questions and requesting viewer opinions or experiences."
      },
      {
        tip: "Create community posts",
        description: "Use YouTube's community tab to share updates, polls, and behind-the-scenes content."
      },
      {
        tip: "Host live streams",
        description: "Live streaming creates real-time engagement and helps build a stronger connection with your audience."
      }
    ]
  },
  {
    icon: BarChart3,
    title: "Analytics & Optimization",
    tips: [
      {
        tip: "Monitor your audience retention",
        description: "Identify where viewers drop off and adjust your content structure to maintain engagement."
      },
      {
        tip: "Analyze your best-performing videos",
        description: "Study what made your top videos successful and apply those elements to future content."
      },
      {
        tip: "Track your click-through rates",
        description: "Monitor CTR to understand how effective your thumbnails and titles are at attracting clicks."
      },
      {
        tip: "Use YouTube Analytics insights",
        description: "Regularly review your analytics to understand your audience demographics and viewing patterns."
      }
    ]
  },
  {
    icon: Zap,
    title: "Technical Optimization",
    tips: [
      {
        tip: "Optimize video titles for search",
        description: "Include relevant keywords in your titles while keeping them engaging and click-worthy."
      },
      {
        tip: "Write detailed video descriptions",
        description: "Use the first 125 characters effectively and include relevant keywords throughout your description."
      },
      {
        tip: "Add accurate closed captions",
        description: "Captions improve accessibility and can help with SEO by providing more text for YouTube to index."
      },
      {
        tip: "Use relevant tags strategically",
        description: "Include a mix of broad and specific tags that accurately describe your video content."
      }
    ]
  },
  {
    icon: Lightbulb,
    title: "Growth Hacks",
    tips: [
      {
        tip: "Collaborate with other creators",
        description: "Partner with creators in your niche for cross-promotion and to tap into new audiences."
      },
      {
        tip: "Create YouTube Shorts",
        description: "Short-form content can help you reach new audiences and drive traffic to your long-form videos."
      },
      {
        tip: "Optimize your upload timing",
        description: "Post when your audience is most active to maximize early engagement and algorithmic boost."
      },
      {
        tip: "Cross-promote on other platforms",
        description: "Share your videos on social media, blogs, and other platforms to increase initial views."
      }
    ]
  }
];

const quickWins = [
  "Update your channel art and profile picture",
  "Create eye-catching thumbnails for all videos",
  "Add end screens to promote other videos",
  "Write compelling video descriptions",
  "Respond to all comments within 24 hours",
  "Create a channel trailer for new visitors",
  "Organize your videos into themed playlists",
  "Add cards to promote related content"
];

export default function YouTubeGrowthTipsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'YouTube Growth Tips', href: '/youtube-growth-tips' }]}
            className="mb-8"
          />

          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                <Lightbulb className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-white">
                YouTube Growth Tips
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Discover proven strategies and actionable tips to grow your YouTube channel. 
              From content optimization to audience engagement, learn what actually works in 2025.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <BarChart3 className="w-5 h-5 mr-2" />
              Analyze Your Channel
            </Link>
          </div>

          {/* Quick Wins */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Quick Wins for Immediate Impact
              </h2>
              <p className="text-xl text-gray-300">
                Simple changes you can make today to start growing your channel
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickWins.map((win, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/10">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <p className="text-gray-300 text-sm leading-relaxed">{win}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Detailed Tips */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Comprehensive Growth Strategies
              </h2>
              <p className="text-xl text-gray-300">
                In-depth tips organized by category for systematic channel growth
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {growthTips.map((category, categoryIndex) => {
                const IconComponent = category.icon;
                return (
                  <div key={categoryIndex} className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white">{category.title}</h3>
                    </div>

                    <div className="space-y-4">
                      {category.tips.map((tip, tipIndex) => (
                        <div key={tipIndex} className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                          <h4 className="text-lg font-semibold text-white mb-2">{tip.tip}</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">{tip.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Success Metrics */}
          <div className="mb-20">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
              <h2 className="text-3xl font-bold text-white mb-6 text-center">
                Track Your Growth Success
              </h2>
              
              <div className="grid md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Subscribers</h3>
                  <p className="text-gray-300 text-sm">Monitor monthly growth rate and subscriber quality</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Views</h3>
                  <p className="text-gray-300 text-sm">Track view velocity and total view count trends</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Engagement</h3>
                  <p className="text-gray-300 text-sm">Measure likes, comments, and shares per video</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">Retention</h3>
                  <p className="text-gray-300 text-sm">Analyze audience retention and watch time</p>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Implement These Tips?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Get detailed analytics and personalized growth recommendations for your YouTube channel. 
                See which tips will have the biggest impact on your specific content and audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Get Your Growth Report
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/blog"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Read More Tips
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
