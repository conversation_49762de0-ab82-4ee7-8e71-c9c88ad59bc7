"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Eye, 
  MousePointer, 
  CreditCard,
  FileText,
  Calendar,
  Filter
} from "lucide-react";

interface AnalyticsData {
  totalQueries: number;
  signinRate: number;
  channelSelectionRate: number;
  paymentRate: number;
  reportGenerationRate: number;
  overallConversionRate: number;
  revenueTotal: number;
  avgRevenuePerUser: number;
  topTrafficSources: Array<{ source: string; count: number; conversionRate: number }>;
  dailyStats: Array<{ date: string; queries: number; conversions: number; revenue: number }>;
  funnelData: Array<{ step: string; count: number; rate: number }>;
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("7d");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/analytics?range=${dateRange}`);
      if (!response.ok) throw new Error("Failed to fetch analytics");
      const data = await response.json();
      setAnalytics(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load analytics");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error || "No data available"}</p>
          <button 
            onClick={fetchAnalytics}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">Analytics Dashboard</h1>
            <p className="text-gray-300">Conversion funnel and performance metrics</p>
          </div>
          
          {/* Date Range Filter */}
          <div className="flex items-center gap-4">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white"
            >
              <option value="1d">Last 24 hours</option>
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            {
              title: "Total Queries",
              value: analytics.totalQueries.toLocaleString(),
              icon: Eye,
              color: "from-blue-500 to-cyan-500",
              change: "+12.5%"
            },
            {
              title: "Overall Conversion",
              value: `${analytics.overallConversionRate.toFixed(1)}%`,
              icon: TrendingUp,
              color: "from-green-500 to-emerald-500",
              change: "+2.3%"
            },
            {
              title: "Total Revenue",
              value: `$${analytics.revenueTotal.toLocaleString()}`,
              icon: DollarSign,
              color: "from-yellow-500 to-orange-500",
              change: "+18.7%"
            },
            {
              title: "Avg Revenue/User",
              value: `$${analytics.avgRevenuePerUser.toFixed(2)}`,
              icon: Users,
              color: "from-purple-500 to-pink-500",
              change: "+5.2%"
            }
          ].map((metric, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-xl flex items-center justify-center`}>
                  <metric.icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-green-400 text-sm font-medium">{metric.change}</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-1">{metric.value}</h3>
              <p className="text-gray-300 text-sm">{metric.title}</p>
            </motion.div>
          ))}
        </div>

        {/* Conversion Funnel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 mb-8"
        >
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
            <BarChart3 className="w-6 h-6 text-blue-400" />
            Conversion Funnel
          </h2>
          
          <div className="space-y-4">
            {analytics.funnelData.map((step, index) => (
              <div key={index} className="relative">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">{step.step}</span>
                  <div className="flex items-center gap-4">
                    <span className="text-gray-300">{step.count.toLocaleString()}</span>
                    <span className="text-blue-400 font-medium">{step.rate.toFixed(1)}%</span>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-1000"
                    style={{ width: `${step.rate}%` }}
                  ></div>
                </div>
                {index < analytics.funnelData.length - 1 && (
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                    <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-500"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Traffic Sources & Daily Stats */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Traffic Sources */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
          >
            <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
              <MousePointer className="w-5 h-5 text-green-400" />
              Top Traffic Sources
            </h3>
            
            <div className="space-y-4">
              {analytics.topTrafficSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <div className="text-white font-medium">{source.source}</div>
                    <div className="text-gray-400 text-sm">{source.count} queries</div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 font-medium">{source.conversionRate.toFixed(1)}%</div>
                    <div className="text-gray-400 text-sm">conversion</div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Daily Performance */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.9 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
          >
            <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
              <Calendar className="w-5 h-5 text-purple-400" />
              Daily Performance
            </h3>
            
            <div className="space-y-3">
              {analytics.dailyStats.slice(-7).map((day, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="text-gray-300 text-sm">
                    {new Date(day.date).toLocaleDateString()}
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-white text-sm">{day.queries} queries</span>
                    <span className="text-green-400 text-sm">{day.conversions} conversions</span>
                    <span className="text-yellow-400 text-sm">${day.revenue}</span>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
