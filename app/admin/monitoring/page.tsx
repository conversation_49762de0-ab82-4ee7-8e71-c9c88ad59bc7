"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap,
  Server,
  Database,
  Globe,
  RefreshCw
} from "lucide-react";

interface SystemHealth {
  status: "healthy" | "warning" | "critical";
  uptime: number;
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  queueLength: number;
  databaseConnections: number;
  memoryUsage: number;
  cpuUsage: number;
  lastUpdated: string;
}

interface Alert {
  id: string;
  type: "error" | "warning" | "info";
  message: string;
  timestamp: string;
  resolved: boolean;
}

export default function MonitoringPage() {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchSystemHealth();
    fetchAlerts();

    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchSystemHealth();
        fetchAlerts();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchSystemHealth = async () => {
    try {
      const response = await fetch("/api/admin/health");
      if (response.ok) {
        const data = await response.json();
        setHealth(data);
      }
    } catch (error) {
      console.error("Error fetching system health:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await fetch("/api/admin/alerts");
      if (response.ok) {
        const data = await response.json();
        setAlerts(data.alerts || []);
      }
    } catch (error) {
      console.error("Error fetching alerts:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "text-green-400";
      case "warning": return "text-yellow-400";
      case "critical": return "text-red-400";
      default: return "text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return CheckCircle;
      case "warning": return AlertTriangle;
      case "critical": return AlertTriangle;
      default: return Activity;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading system status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">System Monitoring</h1>
            <p className="text-gray-300">Real-time system health and performance</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                autoRefresh 
                  ? "bg-green-500/20 text-green-400 border border-green-500/30" 
                  : "bg-gray-500/20 text-gray-400 border border-gray-500/30"
              }`}
            >
              <RefreshCw className={`w-4 h-4 ${autoRefresh ? "animate-spin" : ""}`} />
              Auto Refresh
            </button>
            
            <button
              onClick={() => {
                fetchSystemHealth();
                fetchAlerts();
              }}
              className="bg-blue-500/20 text-blue-400 border border-blue-500/30 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors"
            >
              Refresh Now
            </button>
          </div>
        </div>

        {/* System Status Overview */}
        {health && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                {(() => {
                  const StatusIcon = getStatusIcon(health.status);
                  return <StatusIcon className={`w-6 h-6 ${getStatusColor(health.status)}`} />;
                })()}
                System Status: <span className={getStatusColor(health.status)}>{health.status.toUpperCase()}</span>
              </h2>
              <div className="text-sm text-gray-400">
                Last updated: {new Date(health.lastUpdated).toLocaleTimeString()}
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
              {[
                {
                  label: "Uptime",
                  value: `${(health.uptime / 3600).toFixed(1)}h`,
                  icon: Clock,
                  color: "text-green-400"
                },
                {
                  label: "Response Time",
                  value: `${health.responseTime}ms`,
                  icon: Zap,
                  color: health.responseTime > 1000 ? "text-red-400" : "text-green-400"
                },
                {
                  label: "Error Rate",
                  value: `${health.errorRate.toFixed(2)}%`,
                  icon: AlertTriangle,
                  color: health.errorRate > 5 ? "text-red-400" : "text-green-400"
                },
                {
                  label: "Active Users",
                  value: health.activeUsers.toString(),
                  icon: Globe,
                  color: "text-blue-400"
                },
                {
                  label: "Queue Length",
                  value: health.queueLength.toString(),
                  icon: Server,
                  color: health.queueLength > 100 ? "text-yellow-400" : "text-green-400"
                },
                {
                  label: "DB Connections",
                  value: health.databaseConnections.toString(),
                  icon: Database,
                  color: health.databaseConnections > 80 ? "text-red-400" : "text-green-400"
                }
              ].map((metric, index) => (
                <div key={index} className="text-center">
                  <metric.icon className={`w-8 h-8 ${metric.color} mx-auto mb-2`} />
                  <div className={`text-2xl font-bold ${metric.color} mb-1`}>{metric.value}</div>
                  <div className="text-sm text-gray-400">{metric.label}</div>
                </div>
              ))}
            </div>

            {/* Resource Usage */}
            <div className="mt-8 grid md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">Memory Usage</span>
                  <span className="text-gray-300">{health.memoryUsage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div 
                    className={`h-3 rounded-full transition-all duration-1000 ${
                      health.memoryUsage > 80 ? "bg-red-500" : 
                      health.memoryUsage > 60 ? "bg-yellow-500" : "bg-green-500"
                    }`}
                    style={{ width: `${health.memoryUsage}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">CPU Usage</span>
                  <span className="text-gray-300">{health.cpuUsage.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3">
                  <div 
                    className={`h-3 rounded-full transition-all duration-1000 ${
                      health.cpuUsage > 80 ? "bg-red-500" : 
                      health.cpuUsage > 60 ? "bg-yellow-500" : "bg-green-500"
                    }`}
                    style={{ width: `${health.cpuUsage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Alerts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
        >
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
            <AlertTriangle className="w-6 h-6 text-yellow-400" />
            Recent Alerts
          </h2>

          {alerts.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <p className="text-gray-300">No active alerts - system running smoothly!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {alerts.slice(0, 10).map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border ${
                    alert.type === "error" ? "bg-red-500/10 border-red-500/30" :
                    alert.type === "warning" ? "bg-yellow-500/10 border-yellow-500/30" :
                    "bg-blue-500/10 border-blue-500/30"
                  } ${alert.resolved ? "opacity-50" : ""}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className={`w-5 h-5 mt-0.5 ${
                        alert.type === "error" ? "text-red-400" :
                        alert.type === "warning" ? "text-yellow-400" :
                        "text-blue-400"
                      }`} />
                      <div>
                        <p className="text-white font-medium">{alert.message}</p>
                        <p className="text-gray-400 text-sm">
                          {new Date(alert.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    {alert.resolved && (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
