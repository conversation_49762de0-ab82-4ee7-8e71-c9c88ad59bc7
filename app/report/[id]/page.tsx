import { Suspense } from "react";

async function fetchReportUrl(id: string) {
  const base = process.env.APP_BASE_URL || "";
  const url = base ? `${base}/api/reports/${id}` : `/api/reports/${id}`;
  const res = await fetch(url, { cache: "no-store" });
  return res.json();
}

export default async function ReportPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const data = await fetchReportUrl(id);
  return (
    <div className="min-h-screen p-6 max-w-3xl mx-auto">
      <h1 className="text-2xl font-semibold mb-4">Rapor #{id}</h1>
      <Suspense fallback={<div>Yükleniyor...</div>}>
        <pre className="text-sm whitespace-pre-wrap break-words bg-gray-50 p-4 rounded border">
          {JSON.stringify(data, null, 2)}
        </pre>
        {data?.jsonUrl && (
          <a href={data.jsonUrl} className="underline" target="_blank" rel="noreferrer">
            JSON indir
          </a>
        )}
      </Suspense>
    </div>
  );
}


