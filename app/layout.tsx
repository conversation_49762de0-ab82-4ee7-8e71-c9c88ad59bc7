import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import FirebaseProvider from "./providers/FirebaseProvider";
import QueryProvider from "./providers/QueryProvider";
import AuthProvider from "./providers/AuthProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "YTuber - AI-Powered YouTube Growth Analytics 2025 | Master the Algorithm",
  description: "Master the 2025 YouTube algorithm with AI-powered analytics. Get viral content ideas, growth strategies, and real-time insights to accelerate your channel growth.",
  keywords: "YouTube algorithm 2025, AI YouTube analytics, YouTube growth strategies, viral content ideas, YouTube algorithm insights, YouTube growth tools, YouTube channel optimization, YouTube analytics dashboard, YouTube growth tracker, YouTube algorithm mastery",
  authors: [{ name: "YTuber Team" }],
  creator: "YTuber Team",
  publisher: "YTuber",

  // Open Graph
  openGraph: {
    title: "YTuber - AI-Powered YouTube Growth Analytics 2025 | Master the Algorithm",
    description: "Master the 2025 YouTube algorithm with AI-powered analytics. Get viral content ideas, growth strategies, and real-time insights.",
    type: "website",
    url: "https://ytuber.life",
    images: [
      {
        url: "https://ytuber.life/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "YTuber - YouTube Growth Analytics",
      }
    ],
    locale: "en_US",
    siteName: "YTuber",
  },

  // Twitter
  twitter: {
    card: "summary_large_image",
    title: "YTuber - AI-Powered YouTube Growth Analytics 2025 | Master the Algorithm",
    description: "Master the 2025 YouTube algorithm with AI-powered analytics. Get viral content ideas, growth strategies, and real-time insights.",
    images: ["https://ytuber.life/og-image.jpg"],
    creator: "@ytuber",
    site: "@ytuber",
  },

  // Robots
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },

  // Additional meta tags
  other: {
    'theme-color': '#3b82f6',
    'color-scheme': 'dark',
    'format-detection': 'telephone=no',
  },

  // Verification (add your codes when available)
  verification: {
    // google: 'your-google-verification-code',
    // bing: 'your-bing-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="canonical" href="https://ytuber.life" />

        {/* Favicons */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme and mobile */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="color-scheme" content="dark" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="YTuber" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <FirebaseProvider>
            <QueryProvider>
              {children}
            </QueryProvider>
          </FirebaseProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
