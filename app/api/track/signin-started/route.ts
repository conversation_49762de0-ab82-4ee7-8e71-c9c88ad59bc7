import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";

export async function PUT(req: NextRequest) {
  try {
    const { queryId } = await req.json();
    
    if (!queryId) {
      return NextResponse.json({ error: "queryId required" }, { status: 400 });
    }

    // Update the query record
    await adminDb.collection('urlQueries').doc(queryId).update({
      signinStarted: true,
      updatedAt: new Date().toISOString()
    });
    
    return NextResponse.json({ message: "Sign-in started tracked" });
  } catch (error) {
    console.error("Error tracking sign-in started:", error);
    return NextResponse.json({ error: "Failed to track sign-in started" }, { status: 500 });
  }
}
