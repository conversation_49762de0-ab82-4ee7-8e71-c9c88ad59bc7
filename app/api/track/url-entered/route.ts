import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { channelUrl, referrer, userAgent, abTestVariant } = await req.json();

    if (!channelUrl) {
      return NextResponse.json({ error: "channelUrl required" }, { status: 400 });
    }

    // Get client IP
    const forwarded = req.headers.get("x-forwarded-for");
    const ip = forwarded ? forwarded.split(",")[0] : req.headers.get("x-real-ip") || "unknown";

    // Generate a mock query ID for now
    const queryId = `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Log the tracking data (in production, save to database)
    console.log("URL Entry Tracked:", {
      queryId,
      channelUrl,
      ipAddress: ip,
      userAgent: userAgent || null,
      referrer: referrer || null,
      abTestVariant: abTestVariant || null,
      timestamp: new Date().toISOString()
    });

    // For now, return success with mock query ID
    // In production, you would save this to Firebase/database
    return NextResponse.json({
      queryId,
      message: "URL query tracked successfully"
    });
  } catch (error) {
    console.error("Error tracking URL entry:", error);
    return NextResponse.json({ error: "Failed to track URL entry" }, { status: 500 });
  }
}
