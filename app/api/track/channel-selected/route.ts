import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";

export async function PUT(req: NextRequest) {
  try {
    const { queryId, channelId } = await req.json();

    if (!queryId || !channelId) {
      return NextResponse.json({ error: "queryId and channelId required" }, { status: 400 });
    }

    // Best-effort tracking: don't fail the user flow if Firestore isn't configured
    try {
      await adminDb.collection('urlQueries').doc(queryId).set({
        channelSelected: true,
        selectedChannelId: channelId,
        updatedAt: new Date().toISOString()
      }, { merge: true });
    } catch (e) {
      console.warn("Tracking skipped (Firestore unavailable):", (e as Error).message);
    }

    return NextResponse.json({ message: "Channel selection tracked" });
  } catch (error) {
    console.error("Error tracking channel selection:", error);
    // Do not block client: return success with note
    return NextResponse.json({ message: "Tracking skipped" });
  }
}
