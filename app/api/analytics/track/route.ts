import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { event, properties } = await req.json();

    if (!event) {
      return NextResponse.json({ error: "event name required" }, { status: 400 });
    }

    // Get client IP and user agent
    const forwarded = req.headers.get("x-forwarded-for");
    const ip = forwarded ? forwarded.split(",")[0] : req.headers.get("x-real-ip") || "unknown";
    const userAgent = req.headers.get("user-agent") || "unknown";

    // Log event data (in production, save to database)
    console.log("Custom Event Tracked:", {
      event,
      properties: properties || {},
      metadata: {
        ip,
        userAgent,
        timestamp: new Date().toISOString(),
        url: properties?.url || null,
        referrer: properties?.referrer || null
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Custom event tracking error:", error);
    return NextResponse.json({ error: "Failed to track event" }, { status: 500 });
  }
}
