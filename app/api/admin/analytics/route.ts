import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";
import dayjs from "dayjs";

export async function GET(req: NextRequest) {
  try {
    // Simple admin check - in production, implement proper admin authentication
    const authHeader = req.headers.get("authorization");
    if (!authHeader?.includes("admin")) {
      // For now, allow access - implement proper admin auth later
      // return NextResponse.json({ error: "Admin access required" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const range = searchParams.get("range") || "7d";

    // Calculate date range
    const endDate = dayjs();
    let startDate = dayjs();
    
    switch (range) {
      case "1d":
        startDate = endDate.subtract(1, "day");
        break;
      case "7d":
        startDate = endDate.subtract(7, "day");
        break;
      case "30d":
        startDate = endDate.subtract(30, "day");
        break;
      case "90d":
        startDate = endDate.subtract(90, "day");
        break;
      default:
        startDate = endDate.subtract(7, "day");
    }

    // Fetch URL queries data
    const urlQueriesSnapshot = await adminDb
      .collection("urlQueries")
      .where("createdAt", ">=", startDate.toISOString())
      .where("createdAt", "<=", endDate.toISOString())
      .get();

    const urlQueries = urlQueriesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as any[]; // eslint-disable-line @typescript-eslint/no-explicit-any

    // Calculate funnel metrics
    const totalQueries = urlQueries.length;
    const signinCompleted = urlQueries.filter(q => q.signinCompleted).length;
    const channelSelected = urlQueries.filter(q => q.channelSelected).length;
    const paymentCompleted = urlQueries.filter(q => q.paymentCompleted).length;
    const reportGenerated = urlQueries.filter(q => q.reportGenerated).length;

    // Calculate rates
    const signinRate = totalQueries > 0 ? (signinCompleted / totalQueries) * 100 : 0;
    const channelSelectionRate = signinCompleted > 0 ? (channelSelected / signinCompleted) * 100 : 0;
    const paymentRate = channelSelected > 0 ? (paymentCompleted / channelSelected) * 100 : 0;
    const reportGenerationRate = paymentCompleted > 0 ? (reportGenerated / paymentCompleted) * 100 : 0;
    const overallConversionRate = totalQueries > 0 ? (paymentCompleted / totalQueries) * 100 : 0;

    // Calculate revenue metrics
    const revenueTotal = urlQueries
      .filter(q => q.paymentCompleted && q.paymentAmount)
      .reduce((sum, q) => sum + (q.paymentAmount || 0), 0);
    
    const avgRevenuePerUser = paymentCompleted > 0 ? revenueTotal / paymentCompleted : 0;

    // Traffic sources analysis
    const trafficSources = urlQueries.reduce((acc: any, query) => { // eslint-disable-line @typescript-eslint/no-explicit-any
      const source = query.referrer || query.utmSource || "Direct";
      if (!acc[source]) {
        acc[source] = { count: 0, conversions: 0 };
      }
      acc[source].count++;
      if (query.paymentCompleted) {
        acc[source].conversions++;
      }
      return acc;
    }, {});

    const topTrafficSources = Object.entries(trafficSources)
      .map(([source, data]: [string, any]) => ({ // eslint-disable-line @typescript-eslint/no-explicit-any
        source,
        count: data.count,
        conversionRate: data.count > 0 ? (data.conversions / data.count) * 100 : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Daily stats
    const dailyStats = [];
    for (let i = 0; i < parseInt(range.replace("d", "")); i++) {
      const date = endDate.subtract(i, "day");
      const dayStart = date.startOf("day").toISOString();
      const dayEnd = date.endOf("day").toISOString();
      
      const dayQueries = urlQueries.filter(q => 
        q.createdAt >= dayStart && q.createdAt <= dayEnd
      );
      
      const dayConversions = dayQueries.filter(q => q.paymentCompleted).length;
      const dayRevenue = dayQueries
        .filter(q => q.paymentCompleted && q.paymentAmount)
        .reduce((sum, q) => sum + (q.paymentAmount || 0), 0);

      dailyStats.unshift({
        date: date.format("YYYY-MM-DD"),
        queries: dayQueries.length,
        conversions: dayConversions,
        revenue: dayRevenue
      });
    }

    // Funnel data for visualization
    const funnelData = [
      {
        step: "URL Entered",
        count: totalQueries,
        rate: 100
      },
      {
        step: "Sign-in Completed",
        count: signinCompleted,
        rate: signinRate
      },
      {
        step: "Channel Selected",
        count: channelSelected,
        rate: totalQueries > 0 ? (channelSelected / totalQueries) * 100 : 0
      },
      {
        step: "Payment Completed",
        count: paymentCompleted,
        rate: totalQueries > 0 ? (paymentCompleted / totalQueries) * 100 : 0
      },
      {
        step: "Report Generated",
        count: reportGenerated,
        rate: totalQueries > 0 ? (reportGenerated / totalQueries) * 100 : 0
      }
    ];

    const analytics = {
      totalQueries,
      signinRate,
      channelSelectionRate,
      paymentRate,
      reportGenerationRate,
      overallConversionRate,
      revenueTotal,
      avgRevenuePerUser,
      topTrafficSources,
      dailyStats,
      funnelData,
      
      // Additional insights
      insights: {
        biggestDropOff: funnelData.reduce((max, current, index, array) => {
          if (index === 0) return max;
          const dropOff = array[index - 1].rate - current.rate;
          return dropOff > max.dropOff ? { step: current.step, dropOff } : max;
        }, { step: "", dropOff: 0 }),
        
        bestConvertingSource: topTrafficSources[0] || null,
        
        trends: {
          queriesGrowth: dailyStats.length >= 2 ? 
            ((dailyStats[dailyStats.length - 1].queries - dailyStats[dailyStats.length - 2].queries) / 
             Math.max(dailyStats[dailyStats.length - 2].queries, 1)) * 100 : 0,
          
          revenueGrowth: dailyStats.length >= 2 ?
            ((dailyStats[dailyStats.length - 1].revenue - dailyStats[dailyStats.length - 2].revenue) /
             Math.max(dailyStats[dailyStats.length - 2].revenue, 1)) * 100 : 0
        }
      }
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error("Analytics API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics data" },
      { status: 500 }
    );
  }
}
