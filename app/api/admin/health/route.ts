import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";

export async function GET(req: NextRequest) {
  try {
    const startTime = Date.now();

    // Test database connectivity
    let dbStatus = "healthy";
    let dbResponseTime = 0;
    try {
      const dbStart = Date.now();
      await adminDb.collection("health").doc("test").get();
      dbResponseTime = Date.now() - dbStart;
    } catch (error) {
      dbStatus = "critical";
      dbResponseTime = -1;
      await ErrorHandler.logError(error as Error, {
        endpoint: "/api/admin/health",
        method: "GET"
      }, "critical");
    }

    // Get system metrics (mock data - in production, use actual system monitoring)
    const systemMetrics = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: Math.random() * 100, // Mock CPU usage
    };

    // Calculate memory usage percentage
    const memoryUsagePercent = (systemMetrics.memoryUsage.heapUsed / systemMetrics.memoryUsage.heapTotal) * 100;

    // Get real metrics from Firebase
    const [usersSnapshot, reportsSnapshot, errorsSnapshot] = await Promise.all([
      adminDb.collection('users').count().get(),
      adminDb.collection('reports').where('createdAt', '>=', new Date(Date.now() - 24 * 60 * 60 * 1000)).count().get(),
      adminDb.collection('errors').where('createdAt', '>=', new Date(Date.now() - 60 * 60 * 1000)).count().get()
    ]);

    const activeUsers = usersSnapshot.data().count;
    const reportsLast24h = reportsSnapshot.data().count;
    const errorsLastHour = errorsSnapshot.data().count;
    const errorRate = reportsLast24h > 0 ? (errorsLastHour / reportsLast24h) * 100 : 0;

    // Determine overall system status
    let overallStatus = "healthy";
    if (dbStatus === "critical" || errorRate > 10 || memoryUsagePercent > 90) {
      overallStatus = "critical";
    } else if (errorRate > 5 || memoryUsagePercent > 80 || reportsLast24h > 1000) {
      overallStatus = "warning";
    }

    const responseTime = Date.now() - startTime;

    const health = {
      status: overallStatus,
      uptime: systemMetrics.uptime,
      responseTime,
      errorRate,
      activeUsers,
      reportsLast24h,
      memoryUsage: memoryUsagePercent,
      cpuUsage: systemMetrics.cpuUsage,
      lastUpdated: new Date().toISOString(),
      
      // Detailed component status
      components: {
        database: {
          status: dbStatus,
          responseTime: dbResponseTime,
          activeUsers: activeUsers
        },
        api: {
          status: responseTime < 1000 ? "healthy" : "warning",
          responseTime,
          errorRate
        },
        memory: {
          status: memoryUsagePercent > 80 ? "warning" : "healthy",
          usage: memoryUsagePercent,
          total: systemMetrics.memoryUsage.heapTotal,
          used: systemMetrics.memoryUsage.heapUsed
        },
        cpu: {
          status: systemMetrics.cpuUsage > 80 ? "warning" : "healthy",
          usage: systemMetrics.cpuUsage
        }
      }
    };

    // Store health check result for historical tracking
    try {
      await adminDb.collection("healthChecks").add({
        ...health,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error("Failed to store health check:", error);
    }

    return NextResponse.json(health);
  } catch (error) {
    console.error("Health check error:", error);
    
    return NextResponse.json({
      status: "critical",
      uptime: 0,
      responseTime: -1,
      errorRate: 100,
      activeUsers: 0,
      queueLength: 0,
      databaseConnections: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      lastUpdated: new Date().toISOString(),
      error: "Health check failed"
    }, { status: 500 });
  }
}
