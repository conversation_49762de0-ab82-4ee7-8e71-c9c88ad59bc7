import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";
import dayjs from "dayjs";

export async function GET(req: NextRequest) {
  try {
    // Get recent alerts from the last 24 hours
    const yesterday = dayjs().subtract(24, "hour").toISOString();
    
    const alertsSnapshot = await adminDb
      .collection("alerts")
      .where("timestamp", ">=", yesterday)
      .orderBy("timestamp", "desc")
      .limit(50)
      .get();

    const alerts = alertsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp || new Date().toISOString()
    }));

    // Generate some mock alerts for demonstration
    const mockAlerts = [
      {
        id: "alert_1",
        type: "warning",
        message: "High memory usage detected (85%)",
        timestamp: dayjs().subtract(2, "hour").toISOString(),
        resolved: false
      },
      {
        id: "alert_2", 
        type: "info",
        message: "Scheduled maintenance completed successfully",
        timestamp: dayjs().subtract(4, "hour").toISOString(),
        resolved: true
      },
      {
        id: "alert_3",
        type: "error",
        message: "Database connection timeout (resolved)",
        timestamp: dayjs().subtract(6, "hour").toISOString(),
        resolved: true
      }
    ];

    // Combine real alerts with mock alerts (remove mock alerts in production)
    const allAlerts = [...alerts, ...mockAlerts].sort((a: any, b: any) => // eslint-disable-line @typescript-eslint/no-explicit-any
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    return NextResponse.json({
      alerts: allAlerts,
      summary: {
        total: allAlerts.length,
        unresolved: allAlerts.filter((alert: any) => !alert.resolved).length, // eslint-disable-line @typescript-eslint/no-explicit-any
        critical: allAlerts.filter((alert: any) => alert.type === "error" && !alert.resolved).length, // eslint-disable-line @typescript-eslint/no-explicit-any
        warnings: allAlerts.filter((alert: any) => alert.type === "warning" && !alert.resolved).length // eslint-disable-line @typescript-eslint/no-explicit-any
      }
    });
  } catch (error) {
    console.error("Alerts API error:", error);
    return NextResponse.json({ error: "Failed to fetch alerts" }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { type, message, metadata } = await req.json();
    
    if (!type || !message) {
      return NextResponse.json({ error: "type and message required" }, { status: 400 });
    }

    const alert = {
      type,
      message,
      metadata: metadata || {},
      timestamp: new Date().toISOString(),
      resolved: false,
      resolvedAt: null,
      resolvedBy: null
    };

    const docRef = await adminDb.collection("alerts").add(alert);
    
    // In production, you might want to send notifications here
    // - Email alerts for critical issues
    // - Slack/Discord webhooks
    // - SMS for urgent alerts
    
    return NextResponse.json({ 
      id: docRef.id,
      message: "Alert created successfully" 
    });
  } catch (error) {
    console.error("Create alert error:", error);
    return NextResponse.json({ error: "Failed to create alert" }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { alertId, resolved, resolvedBy } = await req.json();
    
    if (!alertId) {
      return NextResponse.json({ error: "alertId required" }, { status: 400 });
    }

    const updateData: any = { // eslint-disable-line @typescript-eslint/no-explicit-any
      resolved: resolved || false,
      updatedAt: new Date().toISOString()
    };

    if (resolved) {
      updateData.resolvedAt = new Date().toISOString();
      updateData.resolvedBy = resolvedBy || "system";
    }

    await adminDb.collection("alerts").doc(alertId).update(updateData);
    
    return NextResponse.json({ message: "Alert updated successfully" });
  } catch (error) {
    console.error("Update alert error:", error);
    return NextResponse.json({ error: "Failed to update alert" }, { status: 500 });
  }
}
