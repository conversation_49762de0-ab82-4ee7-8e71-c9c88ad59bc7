import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  const requestId = `reports_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[user/reports] ${requestId} - Fetching user reports`);

    const uid = await verify(req);
    if (!uid) {
      console.error(`[user/reports] ${requestId} - Unauthorized access attempt`);
      await <PERSON>rror<PERSON>andler.logError("Unauthorized access to user reports", {
        endpoint: "/api/user/reports",
        method: "GET"
      }, "medium");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`[user/reports] ${requestId} - User authenticated:`, uid);

    const snap = await adminDb
      .collection("reports")
      .where("uid", "==", uid)
      .orderBy("createdAt", "desc")
      .limit(50)
      .get();

    const reports = snap.docs.map((d) => ({ id: d.id, ...d.data() }));
    console.log(`[user/reports] ${requestId} - Found ${reports.length} reports for user`);

    return NextResponse.json({ reports });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Failed to load reports";
    console.error(`[user/reports] ${requestId} - Error:`, message);
    await ErrorHandler.logError(e as Error, {
      userId: uid || "unknown",
      endpoint: "/api/user/reports",
      method: "GET",
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


