import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  try {
    const uid = await verify(req);
    if (!uid) return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

    // Purchases are stored in Firestore under purchases/{token}
    const snap = await adminDb
      .collection("purchases")
      .where("uid", "==", uid)
      .orderBy("createdAt", "desc")
      .limit(50)
      .get();

    const purchases = snap.docs.map((d) => ({ id: d.id, ...d.data() }));

    return NextResponse.json({ purchases });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Failed to fetch purchases";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

