import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  const requestId = `purchases_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  let uid: string | null = null;

  try {
    console.log(`[user/purchases] ${requestId} - Fetching user purchases`);

    uid = await verify(req);
    if (!uid) {
      console.error(`[user/purchases] ${requestId} - Unauthorized access attempt`);
      await ErrorHandler.logError("Unauthorized access to user purchases", {
        endpoint: "/api/user/purchases",
        method: "GET"
      }, "medium");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`[user/purchases] ${requestId} - User authenticated:`, uid);

    // Purchases are stored in Firestore under purchases/{token}
    const snap = await adminDb
      .collection("purchases")
      .where("uid", "==", uid)
      .orderBy("createdAt", "desc")
      .limit(50)
      .get();

    const purchases = snap.docs.map((d) => ({ id: d.id, ...d.data() }));
    console.log(`[user/purchases] ${requestId} - Found ${purchases.length} purchases for user`);

    return NextResponse.json({ purchases });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Failed to fetch purchases";
    console.error(`[user/purchases] ${requestId} - Error:`, message);
    await ErrorHandler.logError(e as Error, {
      userId: uid || "unknown",
      endpoint: "/api/user/purchases",
      method: "GET"
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}

