import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  try {
    const uid = await verify(req);
    if (!uid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const doc = await adminDb.collection("users").doc(uid).get();
    const data = doc.exists ? doc.data() : null;
    return NextResponse.json({
      selectedChannelId: data?.selectedChannelId || null,
      selectedChannelName: data?.selectedChannelName || null,
    });
  } catch (error) {
    console.error("selected-channel error:", error);
    return NextResponse.json({ selectedChannelId: null }, { status: 200 });
  }
}

