import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";
import { getUserPackInfo, PACK_CONFIGS } from "@/lib/pack-system";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  const uid = await verify(req);
  if (!uid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Try primary source (Prisma)
  try {
    const packInfo = await getUserPackInfo(uid);
    return NextResponse.json(packInfo);
  } catch (prismaErr) {
    console.warn("[pack-info] Prisma fallback to Firestore:", (prismaErr as Error).message);
    try {
      const snap = await adminDb.collection("users").doc(uid).get();
      const data = snap.exists ? (snap.data() as any) : {};
      const currentPack = data.currentPack ?? null;
      const packConfig = currentPack ? PACK_CONFIGS[currentPack] : null;
      return NextResponse.json({
        currentPack,
        packCredits: data.packCredits ?? data.credits ?? 0,
        hasMemoryAccess: data.hasMemoryAccess ?? !!packConfig?.hasMemoryAccess,
        isFirstTimer: data.isFirstTimer ?? true,
        packConfig
      });
    } catch (e) {
      console.error("[pack-info] Firestore fallback failed:", (e as Error).message);
      return NextResponse.json({
        error: "Failed to fetch pack info",
        packCredits: 0,
        currentPack: null,
        isFirstTimer: true,
        hasMemoryAccess: false
      }, { status: 500 });
    }
  }
}
