import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";
import { getUserPackInfo, PACK_CONFIGS } from "@/lib/pack-system";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";

async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}

export async function GET(req: NextRequest) {
  const requestId = `packinfo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  let uid: string | null = null;

  try {
    console.log(`[user/pack-info] ${requestId} - Fetching user pack info`);

    uid = await verify(req);
    if (!uid) {
      console.error(`[user/pack-info] ${requestId} - Unauthorized access attempt`);
      await ErrorHandler.logError("Unauthorized access to pack info", {
        endpoint: "/api/user/pack-info",
        method: "GET"
      }, "medium");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`[user/pack-info] ${requestId} - User authenticated:`, uid);

    // Try primary source (pack system)
    try {
      const packInfo = await getUserPackInfo(uid);
      console.log(`[user/pack-info] ${requestId} - Pack info retrieved successfully:`, {
        currentPack: packInfo.currentPack,
        packCredits: packInfo.packCredits,
        hasMemoryAccess: packInfo.hasMemoryAccess
      });
      return NextResponse.json(packInfo);
    } catch (prismaErr) {
      console.warn(`[user/pack-info] ${requestId} - Pack system fallback to Firestore:`, (prismaErr as Error).message);
      await ErrorHandler.logError(prismaErr as Error, {
        userId: uid,
        endpoint: "/api/user/pack-info",
        method: "GET"
      }, "medium");

      try {
        const snap = await adminDb.collection("users").doc(uid).get();
        const data = snap.exists ? (snap.data() as any) : {};
        const currentPack = data.currentPack ?? null;
        const packConfig = currentPack ? PACK_CONFIGS[currentPack] : null;

        const fallbackInfo = {
          currentPack,
          packCredits: data.packCredits ?? data.credits ?? 0,
          hasMemoryAccess: data.hasMemoryAccess ?? !!packConfig?.hasMemoryAccess,
          isFirstTimer: data.isFirstTimer ?? true,
          packConfig
        };

        console.log(`[user/pack-info] ${requestId} - Firestore fallback successful:`, fallbackInfo);
        return NextResponse.json(fallbackInfo);
      } catch (e) {
        console.error(`[user/pack-info] ${requestId} - Firestore fallback failed:`, (e as Error).message);
        await ErrorHandler.logError(e as Error, {
          userId: uid,
          endpoint: "/api/user/pack-info",
          method: "GET"
        }, "high");
        return NextResponse.json({
          error: "Failed to fetch pack info",
          packCredits: 0,
          currentPack: null,
          isFirstTimer: true,
          hasMemoryAccess: false
        }, { status: 500 });
      }
    }
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Failed to fetch pack info";
    console.error(`[user/pack-info] ${requestId} - Unexpected error:`, message);
    await ErrorHandler.logError(e as Error, {
      userId: uid || "unknown",
      endpoint: "/api/user/pack-info",
      method: "GET"
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
