import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";
import { randomUUID } from "crypto";

export async function POST(req: NextRequest) {
  try {
    const { channelUrl, mode = "public", uid = "guest" } = await req.json();
    if (!channelUrl) return NextResponse.json({ error: "channelUrl gerekli" }, { status: 400 });

    // Credits check would normally read from /users/{uid}
    // For now, simulate: if not provided, allow and decrement later in worker

    const jobId = randomUUID();
    const now = new Date();
    await adminDb.collection("jobs").doc(jobId).set({
      uid,
      channelId: null,
      status: "queued",
      createdAt: now,
      mode,
    });

    // TODO: enqueue Cloud Tasks to Cloud Run Job with {jobId}

    return NextResponse.json({ jobId });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Hata";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


