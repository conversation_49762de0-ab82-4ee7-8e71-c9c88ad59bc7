import { NextRequest, NextResponse } from "next/server";
import { resolveChannelIdFromUrl, fetchChannelPreview } from "@/lib/youtube";
import { createYouTubeService } from "@/lib/youtube-analytics";
import { adminDb, adminAuth } from "@/lib/firebase/admin";
import dayjs from "dayjs";

export async function POST(req: NextRequest) {
  try {
    const { channelUrl, channelId: providedChannelId } = await req.json();

    let channelId = providedChannelId;

    // If channelId is provided, use it directly (from YouTube connection)
    // Otherwise, resolve from URL (fallback for old flow)
    if (!channelId && channelUrl) {
      channelId = await resolveChannelIdFromUrl(channelUrl);
    }

    if (!channelId) {
      return NextResponse.json({ error: "channelId or channelUrl required" }, { status: 400 });
    }

    // Try to get YouTube connection for real analytics
    const connectionDoc = await adminDb.collection('youtubeConnections').doc(channelId).get();

    if (connectionDoc.exists) {
      // Use real analytics data
      const connectionData = connectionDoc.data();
      const youtubeService = createYouTubeService(connectionData!.accessToken);

      const endDate = dayjs().format('YYYY-MM-DD');
      const startDate30 = dayjs().subtract(30, 'day').format('YYYY-MM-DD');

      const [analytics30Days, topVideos] = await Promise.all([
        youtubeService.getChannelAnalytics(channelId, startDate30, endDate),
        youtubeService.getTopVideos(channelId, 5)
      ]);

      const preview = {
        channelName: connectionData!.channelName,
        channelId: channelId,
        realAnalytics: true,
        performance: {
          views30Days: analytics30Days.views,
          watchTime30Days: analytics30Days.estimatedMinutesWatched,
          avgViewDuration: analytics30Days.averageViewDuration,
          ctr: analytics30Days.impressionClickThroughRate,
          avgViewPercentage: analytics30Days.averageViewPercentage,
          subscribersGained: analytics30Days.subscribersGained
        },
        topVideos: topVideos.slice(0, 3).map((video: any) => ({ // eslint-disable-line @typescript-eslint/no-explicit-any
          title: video.snippet.title,
          views: parseInt(video.statistics.viewCount).toLocaleString()
        }))
      };

      return NextResponse.json(preview);
    } else {
      // Fallback to basic preview without analytics
      const preview = await fetchChannelPreview(channelId);
      return NextResponse.json({ ...preview, realAnalytics: false });
    }
  } catch (e: unknown) {
    console.error("Preview analysis error:", e);
    const message = e instanceof Error ? e.message : "Analysis failed";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


