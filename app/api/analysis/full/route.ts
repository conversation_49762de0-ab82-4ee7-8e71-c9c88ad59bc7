import { NextRequest, NextResponse } from "next/server";
import { resolveChannelIdFromUrl, fetchChannelData, fetchChannelVideos } from "@/lib/youtube";
import { createYouTubeService } from "@/lib/youtube-analytics";
import { adminDb, adminAuth } from "@/lib/firebase/admin";
import { generateAnalysisWithGemini } from "@/lib/gemini";
import { generateRecommendationsWithGPT } from "@/lib/openai";
import { getUserPackInfo, consumeCredit, getPreviousReport, generateMemoryInsights, refundCredit } from "@/lib/pack-system";
import dayjs from "dayjs";

export async function POST(req: NextRequest) {
  const requestId = `full_${Date.now()}`;
  let creditConsumed = false;
  let reportId: string | null = null;
  let userId: string | null = null;
  const debugStages: string[] = [];
  try {
    const { channelUrl, channelId, queryId } = await req.json();
    if (!channelId) return NextResponse.json({ error: "channelId required", requestId }, { status: 400 });

    // Default channelUrl if missing
    const channelUrlFinal = channelUrl || `https://www.youtube.com/channel/${channelId}`;

    // Derive userId robustly: prefer Firebase token, then session cookie
    try {
      const authHeader = req.headers.get("authorization");
      const bearer = authHeader?.split("Bearer ")[1];
      if (bearer) {
        const decoded = await adminAuth.verifyIdToken(bearer);
        userId = decoded.uid as string;
      }
    } catch {}
    if (!userId) {
      const sessionCookie = (req as any).cookies?.get?.("youtube_session") || undefined as any; // next/server typed
      try {
        // For Next.js app routes, use manual cookie parse from headers if needed
        const cookieHeader = req.headers.get('cookie') || '';
        const match = cookieHeader.split(';').map(s=>s.trim()).find(s=>s.startsWith('youtube_session='));
        const raw = sessionCookie?.value || (match ? decodeURIComponent(match.split('=')[1]) : null);
        if (raw) {
          const session = JSON.parse(Buffer.from(raw, 'base64').toString());
          userId = session?.user?.id || null;
        }
      } catch {}
    }
    if (!userId) return NextResponse.json({ error: "Unauthorized", requestId }, { status: 401 });

    debugStages.push('auth_ok');

    // Check user pack and credits
    const packInfo = await getUserPackInfo(userId);
    if (packInfo.packCredits <= 0) {
      return NextResponse.json({ error: "Insufficient credits", needsPurchase: true, requestId }, { status: 402 });
    }
    debugStages.push(`pack_ok:${packInfo.packCredits}`);

    // Get YouTube connection for this channel
    const connectionDoc = await adminDb.collection('youtubeConnections').doc(channelId).get();
    if (!connectionDoc.exists) {
      return NextResponse.json({ error: "YouTube connection not found" }, { status: 404 });
    }

    const connectionData = connectionDoc.data();
    if (!connectionData) {
      return NextResponse.json({ error: "Invalid connection data", requestId }, { status: 400 });
    }

    debugStages.push('connection_ok');

    // Create YouTube Analytics service with access token
    const youtubeService = createYouTubeService(connectionData.accessToken);
    debugStages.push('yt_service_ok');

    // Calculate date ranges for analytics
    const endDate = dayjs().format('YYYY-MM-DD');
    const startDate90 = dayjs().subtract(90, 'day').format('YYYY-MM-DD');
    const startDate30 = dayjs().subtract(30, 'day').format('YYYY-MM-DD');

    // Fetch data with resilience; do not fail the whole request on a single sub-step
    const results = await Promise.allSettled([
      fetchChannelData(channelId),
      fetchChannelVideos(channelId, 50),
      youtubeService.getChannelAnalytics(channelId, startDate90, endDate),
      youtubeService.getChannelAnalytics(channelId, startDate30, endDate),
      youtubeService.getTopVideos(channelId, 20),
      youtubeService.getTrafficSources(channelId, startDate30, endDate),
      youtubeService.getDemographics(channelId, startDate30, endDate)
    ]);
    debugStages.push('data_fetch_done');

    const channelData = results[0].status === 'fulfilled' ? results[0].value : null as any;
    const videos = results[1].status === 'fulfilled' ? results[1].value : [] as any[];
    const analytics90Days = results[2].status === 'fulfilled' ? results[2].value : {
      views: 0, estimatedMinutesWatched: 0, averageViewDuration: 0,
      subscribersGained: 0, subscribersLost: 0, likes: 0, dislikes: 0, comments: 0,
      shares: 0, impressions: 0, impressionClickThroughRate: 0, averageViewPercentage: 0
    };
    const analytics30Days = results[3].status === 'fulfilled' ? results[3].value : {
      views: 0, estimatedMinutesWatched: 0, averageViewDuration: 0,
      subscribersGained: 0, subscribersLost: 0, likes: 0, dislikes: 0, comments: 0,
      shares: 0, impressions: 0, impressionClickThroughRate: 0, averageViewPercentage: 0
    };
    const topVideos = results[4].status === 'fulfilled' ? results[4].value : [] as any[];
    const trafficSources = results[5].status === 'fulfilled' ? results[5].value : [] as any[];
    const demographics = results[6].status === 'fulfilled' ? results[6].value : { ageGender: [], geography: [] } as any;

    if (results.some((r, i) => r.status === 'rejected')) {
      debugStages.push('data_partial_fail');
      console.error('[analysis/full] Partial failures:', results.map((r, i) => ({ i, status: r.status, reason: (r as any).reason?.message || (r as any).reason })));
    }

    // If channelData missing, fallback to minimal shape to continue
    const channelDataSafe = channelData || {
      id: channelId,
      snippet: { title: channelId, publishedAt: new Date().toISOString() },
      statistics: { subscriberCount: '0', viewCount: '0', videoCount: '0' },
      contentDetails: { relatedPlaylists: { uploads: '' } }
    };

    // Ensure videos arrays defined
    const videosSafe = Array.isArray(videos) ? videos : [];
    const topVideosSafe = Array.isArray(topVideos) ? topVideos : [];


    // Analyze thumbnails and content with Gemini Vision (enhanced with real data)
    let thumbnailAnalysis: any; // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      thumbnailAnalysis = await generateAnalysisWithGemini(topVideosSafe, analytics30Days);
      debugStages.push('gemini_ok');
    } catch (e) {
      console.error('[analysis/full] Gemini analysis failed:', (e as any)?.message || e);
      debugStages.push('gemini_fail');
      thumbnailAnalysis = { performance: {}, principles: [], colors: [], textOptimization: [], suggestions: [] };
    }

    // Generate comprehensive recommendations (fallback on failure)
    let recommendations: any; // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      recommendations = await generateRecommendationsWithGPT({
        channelData: channelDataSafe,
        videos: topVideosSafe,
        thumbnailAnalysis,
        analytics90Days,
        analytics30Days,
        trafficSources,
        demographics
      });
      debugStages.push('gpt_ok');
    } catch (e) {
      console.error('[analysis/full] GPT recommendations failed:', (e as any)?.message || e);
      debugStages.push('gpt_fail');
      recommendations = {
        ctrOptimization: [], watchTimeAnalysis: [], algorithmCompatibility: [], searchOptimization: [],
        swotAnalysis: { strengths: [], weaknesses: [], opportunities: [], threats: [] },
        videoIdeas: [], contentPillars: [], trendingTopics: [], competitorAnalysis: [],
        growthPlan: { shortTerm: [], mediumTerm: [], longTerm: [], milestones: [] },
        immediate: [], priority: [], strategic: [], experimental: [],
        predictions: { viewsGrowth: 0, subscriberGrowth: 0, engagementImprovement: 0, revenueProjection: 0 }
      };
    }

    // Create comprehensive report
    const report = {
      id: `report_${Date.now()}`,
      channelId,
      channelName: channelDataSafe.snippet.title,
      channelUrl: channelUrlFinal,
      userId: userId!,
      status: "completed",
      createdAt: new Date().toISOString(),
      data: {
        // Channel Overview (with real analytics)
        overview: {
          subscribers: parseInt(channelDataSafe.statistics.subscriberCount),
          totalViews: parseInt(channelDataSafe.statistics.viewCount),
          totalVideos: parseInt(channelDataSafe.statistics.videoCount),
          avgViews: Math.round((analytics30Days.views || 0) / 30), // Real daily average
          engagement: analytics30Days.views ? ((analytics30Days.likes + analytics30Days.comments) / analytics30Days.views) * 100 : 0,
          uploadFrequency: calculateUploadFrequency(videosSafe),
          channelAge: dayjs().diff(dayjs(channelDataSafe.snippet.publishedAt), 'month'),
          topPerformingVideo: getTopPerformingVideo(topVideosSafe),
          // Real analytics metrics
          realMetrics: {
            views30Days: analytics30Days.views,
            views90Days: analytics90Days.views,
            watchTime30Days: analytics30Days.estimatedMinutesWatched,
            avgViewDuration: analytics30Days.averageViewDuration,
            subscribersGained30Days: analytics30Days.subscribersGained,
            subscribersLost30Days: analytics30Days.subscribersLost,
            impressions30Days: analytics30Days.impressions,
            ctr30Days: analytics30Days.impressionClickThroughRate,
            avgViewPercentage: analytics30Days.averageViewPercentage
          }
        },

        // Algorithm Analysis
        algorithmAnalysis: {
          ctrOptimization: recommendations.ctrOptimization,
          watchTimeAnalysis: recommendations.watchTimeAnalysis,
          algorithmCompatibility: recommendations.algorithmCompatibility,
          searchOptimization: recommendations.searchOptimization
        },

        // SWOT Analysis
        swotAnalysis: {
          strengths: recommendations.swotAnalysis.strengths,
          weaknesses: recommendations.swotAnalysis.weaknesses,
          opportunities: recommendations.swotAnalysis.opportunities,
          threats: recommendations.swotAnalysis.threats
        },

        // Content Strategy
        contentStrategy: {
          videoIdeas: recommendations.videoIdeas,
          contentPillars: recommendations.contentPillars,
          trendingTopics: recommendations.trendingTopics,
          competitorAnalysis: recommendations.competitorAnalysis
        },

        // Thumbnail Analysis
        thumbnailAnalysis: {
          currentPerformance: thumbnailAnalysis.performance,
          designPrinciples: thumbnailAnalysis.principles,
          colorAnalysis: thumbnailAnalysis.colors,
          textOptimization: thumbnailAnalysis.textOptimization,
          improvementSuggestions: thumbnailAnalysis.suggestions
        },

        // Growth Plan
        growthPlan: {
          shortTerm: recommendations.growthPlan.shortTerm, // 1-4 weeks
          mediumTerm: recommendations.growthPlan.mediumTerm, // 1-3 months
          longTerm: recommendations.growthPlan.longTerm, // 3-12 months
          milestones: recommendations.growthPlan.milestones
        },

        // Actionable Recommendations
        recommendations: {
          immediate: recommendations.immediate, // Do this week
          priority: recommendations.priority, // Do this month
          strategic: recommendations.strategic, // Long-term goals



          experimental: recommendations.experimental // Test these ideas
        },

        // Performance Predictions
        predictions: {
          viewsGrowth: recommendations.predictions.viewsGrowth,
          subscriberGrowth: recommendations.predictions.subscriberGrowth,
          engagementImprovement: recommendations.predictions.engagementImprovement,
          revenueProjection: recommendations.predictions.revenueProjection
        }
      }
    };

    debugStages.push('report_built');


    // Memory System Integration
    let memoryInsights = null;
    let improvementScore = null;
    let previousReportId = null;

    if (packInfo.hasMemoryAccess) {
      const previousReport = await getPreviousReport(userId, channelId);
      if (previousReport) {
        previousReportId = previousReport.id;
        const memoryResult = await generateMemoryInsights(
          report.data,
          previousReport.data
        );
        improvementScore = memoryResult.improvementScore;
        memoryInsights = memoryResult.insights;

        // Add memory insights to report
        (report.data as Record<string, unknown>).memoryInsights = memoryInsights;



        (report.data as Record<string, unknown>).improvementScore = improvementScore;
        (report.data as Record<string, unknown>).previousReportComparison = {
          previousReportDate: previousReport.createdAt,
          improvementScore,
          keyChanges: memoryInsights
        };
      }
    }

    // Save to Firestore first
    reportId = report.id;
    await adminDb.collection('reports').doc(reportId).set({
      ...report,
      uid: userId,
      previousReportId,
      improvementScore,
      memoryInsights: memoryInsights ? JSON.stringify(memoryInsights) : null
    });


    debugStages.push('report_saved');

    // Consume 1 credit only after report is saved successfully
    const consumed = await consumeCredit(userId);
    if (!consumed) {
      // Rollback report if credit couldn't be consumed (race condition or stale pack-info)
      try { if (reportId) await adminDb.collection('reports').doc(reportId).delete(); } catch {}



      return NextResponse.json({ error: "Insufficient credits", needsPurchase: true, requestId }, { status: 402 });
    }
    creditConsumed = true;


    debugStages.push('credit_consumed');

    // Track report generation
    if (queryId) {
      try {
        await adminDb.collection('urlQueries').doc(queryId).update({
          reportGenerated: true,
          reportId: report.id,
          updatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error tracking report generation:', error);
      }
    }

    return NextResponse.json(report);
  } catch (e: unknown) {
    console.error(`[analysis/full] Error (requestId=${requestId}):`, e);
    try {
      if (creditConsumed && userId) {
        console.warn(`[analysis/full] refunding credit due to failure (requestId=${requestId}, userId=${userId})`);
        await refundCredit(userId);
      }
      if (reportId) {
        console.warn(`[analysis/full] deleting partial report due to failure (requestId=${requestId}, reportId=${reportId})`);
        try { await adminDb.collection('reports').doc(reportId).delete(); } catch {}
      }
    } catch (compErr) {
      console.error('[analysis/full] Compensation failed:', compErr);
    }
    const message = e instanceof Error ? e.message : 'Analysis failed';
    return NextResponse.json({ error: message, requestId, debugStages }, { status: 500 });
  }
}

// Helper functions
function calculateEngagementRate(videos: any[]): number { // eslint-disable-line @typescript-eslint/no-explicit-any
  const totalEngagement = videos.reduce((sum, video) => {
    const likes = parseInt(video.statistics.likeCount || '0');
    const comments = parseInt(video.statistics.commentCount || '0');
    const views = parseInt(video.statistics.viewCount || '1');
    return sum + ((likes + comments) / views) * 100;
  }, 0);
  return Math.round((totalEngagement / videos.length) * 100) / 100;
}

function calculateUploadFrequency(videos: any[]): string { // eslint-disable-line @typescript-eslint/no-explicit-any
  if (videos.length < 2) return "Irregular";

  const dates = videos.map(v => dayjs(v.snippet.publishedAt)).sort();
  const intervals = [];

  for (let i = 1; i < Math.min(dates.length, 10); i++) {
    intervals.push(dates[i].diff(dates[i-1], 'day'));
  }

  const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;

  if (avgInterval <= 1) return "Daily";
  if (avgInterval <= 3) return "2-3 times per week";
  if (avgInterval <= 7) return "Weekly";
  if (avgInterval <= 14) return "Bi-weekly";
  return "Monthly or less";
}

function getTopPerformingVideo(videos: any[]) { // eslint-disable-line @typescript-eslint/no-explicit-any
  return videos.reduce((top, video) => {
    const views = parseInt(video.statistics.viewCount);
    const topViews = parseInt(top?.statistics?.viewCount || '0');
    return views > topViews ? video : top;
  }, null);
}
