import { NextRequest, NextResponse } from "next/server";
import { resolveChannelIdFromUrl, fetchChannelData, fetchChannelVideos } from "@/lib/youtube";
import { createYouTubeService } from "@/lib/youtube-analytics";
import { adminDb, adminAuth } from "@/lib/firebase/admin";
import { generateAnalysisWithGemini } from "@/lib/gemini";
import { generateRecommendationsWithGPT } from "@/lib/openai";
import { getUserPackInfo, consumeCredit, getPreviousReport, generateMemoryInsights, refundCredit } from "@/lib/pack-system";
import { ErrorHandler } from "@/lib/error-handler";
import dayjs from "dayjs";

export async function POST(req: NextRequest) {
  const requestId = `full_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  let creditConsumed = false;
  let reportId: string | null = null;
  let userId: string | null = null;
  const debugStages: string[] = [];

  console.log(`[analysis/full] ${requestId} - Starting full analysis request`);

  try {
    const { channelUrl, channelId, queryId } = await req.json();
    console.log(`[analysis/full] ${requestId} - Request data:`, { channelUrl, channelId, queryId });

    if (!channelId) {
      console.error(`[analysis/full] ${requestId} - Missing channelId`);
      await ErrorHandler.logError("Analysis request missing channelId", {
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "medium");
      return NextResponse.json({ error: "channelId required", requestId }, { status: 400 });
    }

    // Default channelUrl if missing
    const channelUrlFinal = channelUrl || `https://www.youtube.com/channel/${channelId}`;
    console.log(`[analysis/full] ${requestId} - Final channel URL:`, channelUrlFinal);

    // Derive userId robustly: prefer Firebase token, then session cookie
    console.log(`[analysis/full] ${requestId} - Attempting user authentication`);
    try {
      const authHeader = req.headers.get("authorization");
      const bearer = authHeader?.split("Bearer ")[1];
      if (bearer) {
        const decoded = await adminAuth.verifyIdToken(bearer);
        userId = decoded.uid as string;
        console.log(`[analysis/full] ${requestId} - User authenticated via Firebase token:`, userId);
      }
    } catch (e) {
      console.log(`[analysis/full] ${requestId} - Firebase token authentication failed:`, (e as Error).message);
    }

    if (!userId) {
      console.log(`[analysis/full] ${requestId} - Attempting session cookie authentication`);
      const sessionCookie = (req as any).cookies?.get?.("youtube_session") || undefined as any; // next/server typed
      try {
        // For Next.js app routes, use manual cookie parse from headers if needed
        const cookieHeader = req.headers.get('cookie') || '';
        const match = cookieHeader.split(';').map(s=>s.trim()).find(s=>s.startsWith('youtube_session='));
        const raw = sessionCookie?.value || (match ? decodeURIComponent(match.split('=')[1]) : null);
        if (raw) {
          const session = JSON.parse(Buffer.from(raw, 'base64').toString());
          userId = session?.user?.id || null;
          console.log(`[analysis/full] ${requestId} - User authenticated via session cookie:`, userId);
        }
      } catch (e) {
        console.log(`[analysis/full] ${requestId} - Session cookie authentication failed:`, (e as Error).message);
      }
    }

    if (!userId) {
      console.error(`[analysis/full] ${requestId} - Authentication failed`);
      await ErrorHandler.logError("Analysis request authentication failed", {
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "medium");
      return NextResponse.json({ error: "Unauthorized", requestId }, { status: 401 });
    }

    debugStages.push('auth_ok');
    console.log(`[analysis/full] ${requestId} - Authentication successful for user:`, userId);

    // Check user pack and credits
    console.log(`[analysis/full] ${requestId} - Checking user pack and credits`);
    const packInfo = await getUserPackInfo(userId);
    console.log(`[analysis/full] ${requestId} - User pack info:`, {
      packCredits: packInfo.packCredits,
      currentPack: packInfo.currentPack,
      hasMemoryAccess: packInfo.hasMemoryAccess
    });

    if (packInfo.packCredits <= 0) {
      console.error(`[analysis/full] ${requestId} - Insufficient credits:`, packInfo.packCredits);
      await ErrorHandler.logError(`Analysis request with insufficient credits: ${packInfo.packCredits}`, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "medium");
      return NextResponse.json({ error: "Insufficient credits", needsPurchase: true, requestId }, { status: 402 });
    }
    debugStages.push(`pack_ok:${packInfo.packCredits}`);

    // Get YouTube connection for this channel
    console.log(`[analysis/full] ${requestId} - Fetching YouTube connection for channel:`, channelId);
    const connectionDoc = await adminDb.collection('youtubeConnections').doc(channelId).get();
    if (!connectionDoc.exists) {
      console.error(`[analysis/full] ${requestId} - YouTube connection not found for channel:`, channelId);
      await ErrorHandler.logError(`YouTube connection not found for channel: ${channelId}`, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      return NextResponse.json({ error: "YouTube connection not found" }, { status: 404 });
    }

    const connectionData = connectionDoc.data();
    if (!connectionData) {
      console.error(`[analysis/full] ${requestId} - Invalid connection data for channel:`, channelId);
      await ErrorHandler.logError(`Invalid YouTube connection data for channel: ${channelId}`, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      return NextResponse.json({ error: "Invalid connection data", requestId }, { status: 400 });
    }

    console.log(`[analysis/full] ${requestId} - YouTube connection found, access token present:`, !!connectionData.accessToken);
    debugStages.push('connection_ok');

    // Create YouTube Analytics service with access token
    console.log(`[analysis/full] ${requestId} - Creating YouTube Analytics service`);
    const youtubeService = createYouTubeService(connectionData.accessToken);
    debugStages.push('yt_service_ok');

    // Calculate date ranges for analytics
    const endDate = dayjs().format('YYYY-MM-DD');
    const startDate90 = dayjs().subtract(90, 'day').format('YYYY-MM-DD');
    const startDate30 = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
    console.log(`[analysis/full] ${requestId} - Date ranges:`, { startDate90, startDate30, endDate });

    // Fetch data with resilience; do not fail the whole request on a single sub-step
    console.log(`[analysis/full] ${requestId} - Starting parallel data fetch from YouTube APIs`);
    const results = await Promise.allSettled([
      fetchChannelData(channelId),
      fetchChannelVideos(channelId, 50),
      youtubeService.getChannelAnalytics(channelId, startDate90, endDate),
      youtubeService.getChannelAnalytics(channelId, startDate30, endDate),
      youtubeService.getTopVideos(channelId, 20),
      youtubeService.getTrafficSources(channelId, startDate30, endDate),
      youtubeService.getDemographics(channelId, startDate30, endDate)
    ]);
    debugStages.push('data_fetch_done');
    console.log(`[analysis/full] ${requestId} - Data fetch completed`);

    const channelData = results[0].status === 'fulfilled' ? results[0].value : null as any;
    const videos = results[1].status === 'fulfilled' ? results[1].value : [] as any[];
    const analytics90Days = results[2].status === 'fulfilled' ? results[2].value : {
      views: 0, estimatedMinutesWatched: 0, averageViewDuration: 0,
      subscribersGained: 0, subscribersLost: 0, likes: 0, dislikes: 0, comments: 0,
      shares: 0, impressions: 0, impressionClickThroughRate: 0, averageViewPercentage: 0
    };
    const analytics30Days = results[3].status === 'fulfilled' ? results[3].value : {
      views: 0, estimatedMinutesWatched: 0, averageViewDuration: 0,
      subscribersGained: 0, subscribersLost: 0, likes: 0, dislikes: 0, comments: 0,
      shares: 0, impressions: 0, impressionClickThroughRate: 0, averageViewPercentage: 0
    };
    const topVideos = results[4].status === 'fulfilled' ? results[4].value : [] as any[];
    const trafficSources = results[5].status === 'fulfilled' ? results[5].value : [] as any[];
    const demographics = results[6].status === 'fulfilled' ? results[6].value : { ageGender: [], geography: [] } as any;

    // Log data fetch results
    const fetchResults = {
      channelData: results[0].status === 'fulfilled',
      videos: results[1].status === 'fulfilled' ? results[1].value?.length : 0,
      analytics90Days: results[2].status === 'fulfilled',
      analytics30Days: results[3].status === 'fulfilled',
      topVideos: results[4].status === 'fulfilled' ? results[4].value?.length : 0,
      trafficSources: results[5].status === 'fulfilled',
      demographics: results[6].status === 'fulfilled'
    };
    console.log(`[analysis/full] ${requestId} - Data fetch results:`, fetchResults);

    if (results.some((r, i) => r.status === 'rejected')) {
      debugStages.push('data_partial_fail');
      const failures = results.map((r, i) => ({
        index: i,
        status: r.status,
        reason: (r as any).reason?.message || (r as any).reason
      })).filter(r => r.status === 'rejected');

      console.error(`[analysis/full] ${requestId} - Partial data fetch failures:`, failures);
      await ErrorHandler.logError(`YouTube data fetch partial failures: ${failures.length} out of ${results.length}`, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "medium");
    }

    // If channelData missing, fallback to minimal shape to continue
    const channelDataSafe = channelData || {
      id: channelId,
      snippet: { title: channelId, publishedAt: new Date().toISOString() },
      statistics: { subscriberCount: '0', viewCount: '0', videoCount: '0' },
      contentDetails: { relatedPlaylists: { uploads: '' } }
    };
    console.log(`[analysis/full] ${requestId} - Channel data safe:`, !!channelData);

    // Ensure videos arrays defined
    const videosSafe = Array.isArray(videos) ? videos : [];
    const topVideosSafe = Array.isArray(topVideos) ? topVideos : [];
    console.log(`[analysis/full] ${requestId} - Video data:`, {
      videosCount: videosSafe.length,
      topVideosCount: topVideosSafe.length
    });

    // Analyze thumbnails and content with Gemini Vision (enhanced with real data)
    console.log(`[analysis/full] ${requestId} - Starting Gemini thumbnail analysis`);
    let thumbnailAnalysis: any; // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      thumbnailAnalysis = await generateAnalysisWithGemini(topVideosSafe, analytics30Days);
      debugStages.push('gemini_ok');
      console.log(`[analysis/full] ${requestId} - Gemini analysis completed successfully`);
    } catch (e) {
      console.error(`[analysis/full] ${requestId} - Gemini analysis failed:`, (e as any)?.message || e);
      await ErrorHandler.logError(e as Error, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      debugStages.push('gemini_fail');
      thumbnailAnalysis = { performance: {}, principles: [], colors: [], textOptimization: [], suggestions: [] };
    }

    // Generate comprehensive recommendations (fallback on failure)
    console.log(`[analysis/full] ${requestId} - Starting GPT recommendations generation`);
    let recommendations: any; // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      recommendations = await generateRecommendationsWithGPT({
        channelData: channelDataSafe,
        videos: topVideosSafe,
        thumbnailAnalysis,
        analytics90Days,
        analytics30Days,
        trafficSources,
        demographics
      });
      debugStages.push('gpt_ok');
      console.log(`[analysis/full] ${requestId} - GPT recommendations completed successfully`);
    } catch (e) {
      console.error(`[analysis/full] ${requestId} - GPT recommendations failed:`, (e as any)?.message || e);
      await ErrorHandler.logError(e as Error, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      debugStages.push('gpt_fail');
      recommendations = {
        ctrOptimization: [], watchTimeAnalysis: [], algorithmCompatibility: [], searchOptimization: [],
        swotAnalysis: { strengths: [], weaknesses: [], opportunities: [], threats: [] },
        videoIdeas: [], contentPillars: [], trendingTopics: [], competitorAnalysis: [],
        growthPlan: { shortTerm: [], mediumTerm: [], longTerm: [], milestones: [] },
        immediate: [], priority: [], strategic: [], experimental: [],
        predictions: { viewsGrowth: 0, subscriberGrowth: 0, engagementImprovement: 0, revenueProjection: 0 }
      };
    }

    // Create comprehensive report
    const report = {
      id: `report_${Date.now()}`,
      channelId,
      channelName: channelDataSafe.snippet.title,
      channelUrl: channelUrlFinal,
      userId: userId!,
      status: "completed",
      createdAt: new Date().toISOString(),
      data: {
        // Channel Overview (with real analytics)
        overview: {
          subscribers: parseInt(channelDataSafe.statistics.subscriberCount),
          totalViews: parseInt(channelDataSafe.statistics.viewCount),
          totalVideos: parseInt(channelDataSafe.statistics.videoCount),
          avgViews: Math.round((analytics30Days.views || 0) / 30), // Real daily average
          engagement: analytics30Days.views ? ((analytics30Days.likes + analytics30Days.comments) / analytics30Days.views) * 100 : 0,
          uploadFrequency: calculateUploadFrequency(videosSafe),
          channelAge: dayjs().diff(dayjs(channelDataSafe.snippet.publishedAt), 'month'),
          topPerformingVideo: getTopPerformingVideo(topVideosSafe),
          // Real analytics metrics
          realMetrics: {
            views30Days: analytics30Days.views,
            views90Days: analytics90Days.views,
            watchTime30Days: analytics30Days.estimatedMinutesWatched,
            avgViewDuration: analytics30Days.averageViewDuration,
            subscribersGained30Days: analytics30Days.subscribersGained,
            subscribersLost30Days: analytics30Days.subscribersLost,
            impressions30Days: analytics30Days.impressions,
            ctr30Days: analytics30Days.impressionClickThroughRate,
            avgViewPercentage: analytics30Days.averageViewPercentage
          }
        },

        // Algorithm Analysis
        algorithmAnalysis: {
          ctrOptimization: recommendations.ctrOptimization,
          watchTimeAnalysis: recommendations.watchTimeAnalysis,
          algorithmCompatibility: recommendations.algorithmCompatibility,
          searchOptimization: recommendations.searchOptimization
        },

        // SWOT Analysis
        swotAnalysis: {
          strengths: recommendations.swotAnalysis.strengths,
          weaknesses: recommendations.swotAnalysis.weaknesses,
          opportunities: recommendations.swotAnalysis.opportunities,
          threats: recommendations.swotAnalysis.threats
        },

        // Content Strategy
        contentStrategy: {
          videoIdeas: recommendations.videoIdeas,
          contentPillars: recommendations.contentPillars,
          trendingTopics: recommendations.trendingTopics,
          competitorAnalysis: recommendations.competitorAnalysis
        },

        // Thumbnail Analysis
        thumbnailAnalysis: {
          currentPerformance: thumbnailAnalysis.performance,
          designPrinciples: thumbnailAnalysis.principles,
          colorAnalysis: thumbnailAnalysis.colors,
          textOptimization: thumbnailAnalysis.textOptimization,
          improvementSuggestions: thumbnailAnalysis.suggestions
        },

        // Growth Plan
        growthPlan: {
          shortTerm: recommendations.growthPlan.shortTerm, // 1-4 weeks
          mediumTerm: recommendations.growthPlan.mediumTerm, // 1-3 months
          longTerm: recommendations.growthPlan.longTerm, // 3-12 months
          milestones: recommendations.growthPlan.milestones
        },

        // Actionable Recommendations
        recommendations: {
          immediate: recommendations.immediate, // Do this week
          priority: recommendations.priority, // Do this month
          strategic: recommendations.strategic, // Long-term goals



          experimental: recommendations.experimental // Test these ideas
        },

        // Performance Predictions
        predictions: {
          viewsGrowth: recommendations.predictions.viewsGrowth,
          subscriberGrowth: recommendations.predictions.subscriberGrowth,
          engagementImprovement: recommendations.predictions.engagementImprovement,
          revenueProjection: recommendations.predictions.revenueProjection
        }
      }
    };

    debugStages.push('report_built');


    // Memory System Integration
    console.log(`[analysis/full] ${requestId} - Starting memory system integration`);
    let memoryInsights = null;
    let improvementScore = null;
    let previousReportId = null;

    if (packInfo.hasMemoryAccess) {
      console.log(`[analysis/full] ${requestId} - User has memory access, checking for previous reports`);
      try {
        const previousReport = await getPreviousReport(userId, channelId);
        if (previousReport) {
          console.log(`[analysis/full] ${requestId} - Previous report found:`, previousReport.id);
          previousReportId = previousReport.id;
          const memoryResult = await generateMemoryInsights(
            report.data,
            previousReport.data
          );
          improvementScore = memoryResult.improvementScore;
          memoryInsights = memoryResult.insights;
          console.log(`[analysis/full] ${requestId} - Memory insights generated, improvement score:`, improvementScore);

          // Add memory insights to report
          (report.data as Record<string, unknown>).memoryInsights = memoryInsights;
          (report.data as Record<string, unknown>).improvementScore = improvementScore;
          (report.data as Record<string, unknown>).previousReportComparison = {
            previousReportDate: previousReport.createdAt,
            improvementScore,
            keyChanges: memoryInsights
          };
        } else {
          console.log(`[analysis/full] ${requestId} - No previous report found for this channel`);
        }
      } catch (e) {
        console.error(`[analysis/full] ${requestId} - Memory system integration failed:`, (e as Error).message);
        await ErrorHandler.logError(e as Error, {
          userId,
          channelId,
          endpoint: "/api/analysis/full",
          method: "POST"
        }, "medium");
      }
    } else {
      console.log(`[analysis/full] ${requestId} - User does not have memory access`);
    }

    // Save to Firestore first
    console.log(`[analysis/full] ${requestId} - Saving report to Firestore`);
    reportId = report.id;
    try {
      await adminDb.collection('reports').doc(reportId).set({
        ...report,
        uid: userId,
        previousReportId,
        improvementScore,
        memoryInsights: memoryInsights ? JSON.stringify(memoryInsights) : null
      });
      console.log(`[analysis/full] ${requestId} - Report saved successfully:`, reportId);
    } catch (e) {
      console.error(`[analysis/full] ${requestId} - Failed to save report:`, (e as Error).message);
      await ErrorHandler.logError(e as Error, {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      throw e;
    }

    debugStages.push('report_saved');

    // Consume 1 credit only after report is saved successfully
    console.log(`[analysis/full] ${requestId} - Consuming user credit`);
    const consumed = await consumeCredit(userId);
    if (!consumed) {
      console.error(`[analysis/full] ${requestId} - Failed to consume credit, rolling back report`);
      // Rollback report if credit couldn't be consumed (race condition or stale pack-info)
      try {
        if (reportId) {
          await adminDb.collection('reports').doc(reportId).delete();
          console.log(`[analysis/full] ${requestId} - Report rollback completed`);
        }
      } catch (rollbackError) {
        console.error(`[analysis/full] ${requestId} - Report rollback failed:`, rollbackError);
      }

      await ErrorHandler.logError("Credit consumption failed after report generation", {
        userId,
        channelId,
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
      return NextResponse.json({ error: "Insufficient credits", needsPurchase: true, requestId }, { status: 402 });
    }
    creditConsumed = true;
    console.log(`[analysis/full] ${requestId} - Credit consumed successfully`);

    debugStages.push('credit_consumed');

    // Track report generation
    if (queryId) {
      console.log(`[analysis/full] ${requestId} - Tracking report generation for query:`, queryId);
      try {
        await adminDb.collection('urlQueries').doc(queryId).update({
          reportGenerated: true,
          reportId: report.id,
          updatedAt: new Date().toISOString()
        });
        console.log(`[analysis/full] ${requestId} - Report generation tracking updated`);
      } catch (error) {
        console.error(`[analysis/full] ${requestId} - Error tracking report generation:`, error);
        await ErrorHandler.logError(error as Error, {
          userId,
          channelId,
          endpoint: "/api/analysis/full",
          method: "POST"
        }, "medium");
      }
    }

    console.log(`[analysis/full] ${requestId} - Analysis completed successfully`);
    return NextResponse.json(report);
  } catch (e: unknown) {
    console.error(`[analysis/full] ${requestId} - Unexpected error:`, e);
    await ErrorHandler.logError(e as Error, {
      userId: userId || "unknown",
      channelId: channelId || "unknown",
      endpoint: "/api/analysis/full",
      method: "POST"
    }, "high");

    try {
      if (creditConsumed && userId) {
        console.warn(`[analysis/full] ${requestId} - Refunding credit due to failure`);
        await refundCredit(userId);
      }
      if (reportId) {
        console.warn(`[analysis/full] ${requestId} - Deleting partial report due to failure`);
        try { await adminDb.collection('reports').doc(reportId).delete(); } catch {}
      }
    } catch (compErr) {
      console.error(`[analysis/full] ${requestId} - Compensation failed:`, compErr);
      await ErrorHandler.logError(compErr as Error, {
        userId: userId || "unknown",
        endpoint: "/api/analysis/full",
        method: "POST"
      }, "high");
    }
    const message = e instanceof Error ? e.message : 'Analysis failed';
    return NextResponse.json({ error: message, requestId, debugStages }, { status: 500 });
  }
}

// Helper functions
function calculateEngagementRate(videos: any[]): number { // eslint-disable-line @typescript-eslint/no-explicit-any
  const totalEngagement = videos.reduce((sum, video) => {
    const likes = parseInt(video.statistics.likeCount || '0');
    const comments = parseInt(video.statistics.commentCount || '0');
    const views = parseInt(video.statistics.viewCount || '1');
    return sum + ((likes + comments) / views) * 100;
  }, 0);
  return Math.round((totalEngagement / videos.length) * 100) / 100;
}

function calculateUploadFrequency(videos: any[]): string { // eslint-disable-line @typescript-eslint/no-explicit-any
  if (videos.length < 2) return "Irregular";

  const dates = videos.map(v => dayjs(v.snippet.publishedAt)).sort();
  const intervals = [];

  for (let i = 1; i < Math.min(dates.length, 10); i++) {
    intervals.push(dates[i].diff(dates[i-1], 'day'));
  }

  const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;

  if (avgInterval <= 1) return "Daily";
  if (avgInterval <= 3) return "2-3 times per week";
  if (avgInterval <= 7) return "Weekly";
  if (avgInterval <= 14) return "Bi-weekly";
  return "Monthly or less";
}

function getTopPerformingVideo(videos: any[]) { // eslint-disable-line @typescript-eslint/no-explicit-any
  return videos.reduce((top, video) => {
    const views = parseInt(video.statistics.viewCount);
    const topViews = parseInt(top?.statistics?.viewCount || '0');
    return views > topViews ? video : top;
  }, null);
}
