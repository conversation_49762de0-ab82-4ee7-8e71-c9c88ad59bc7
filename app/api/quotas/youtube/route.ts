import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Mock quota data for development
    const quota = {
      used: Math.floor(Math.random() * 80) + 10, // Random between 10-90
      limit: 100,
      resetDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
    };

    return NextResponse.json(quota);
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Error";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


