import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const testId = searchParams.get('testId');
    const userId = searchParams.get('userId') || 'anonymous';

    if (!testId) {
      return NextResponse.json({ error: "testId required" }, { status: 400 });
    }

    // Mock A/B test variants
    const variants = ['variant_a', 'variant_b', 'variant_c'];
    const variantId = variants[Math.floor(Math.random() * variants.length)];

    console.log("A/B Test Variant:", { testId, userId, variantId });

    return NextResponse.json({ variantId });
  } catch (error) {
    console.error("A/B test variant error:", error);
    return NextResponse.json({ error: "Failed to get variant" }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { testId, userId } = await req.json();

    if (!testId || !userId) {
      return NextResponse.json({ error: "testId and userId required" }, { status: 400 });
    }

    // Mock A/B test variants
    const variants = ['variant_a', 'variant_b', 'variant_c'];
    const variantId = variants[Math.floor(Math.random() * variants.length)];

    console.log("A/B Test Variant (POST):", { testId, userId, variantId });

    return NextResponse.json({ variantId });
  } catch (error) {
    console.error("A/B test variant error:", error);
    return NextResponse.json({ error: "Failed to get variant" }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { testId, userId, action, conversionValue, revenue } = await req.json();

    if (!testId || !userId || !action) {
      return NextResponse.json({ error: "testId, userId, and action required" }, { status: 400 });
    }

    console.log("A/B Test Tracking:", { testId, userId, action, conversionValue, revenue });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("A/B test tracking error:", error);
    return NextResponse.json({ error: "Failed to track event" }, { status: 500 });
  }
}
