import { NextResponse } from "next/server";
import { adminStorage } from "@/lib/firebase/admin";

export async function GET(req: Request) {
  try {
    // Placeholder: in prod, report doc would have storage paths for json/pdf
    const pathname = new URL(req.url).pathname;
    const id = pathname.split("/api/reports/")[1]?.split("/")[0] ?? "";
    const file = adminStorage.bucket().file(`reports/${id}.json`);
    const [url] = await file.getSignedUrl({ action: "read", expires: Date.now() + 15 * 60 * 1000 });
    return NextResponse.json({ jsonUrl: url });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Hata";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


