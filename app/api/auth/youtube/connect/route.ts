import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/admin";

export async function POST(req: NextRequest) {
  try {
    const { channelId, tokens, queryId } = await req.json();
    
    if (!channelId || !tokens) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Get session data to get user info
    const sessionCookie = req.cookies.get('youtube_session');
    if (!sessionCookie) {
      return NextResponse.json({ error: "No session found" }, { status: 401 });
    }

    const sessionData = JSON.parse(Buffer.from(sessionCookie.value, 'base64').toString());
    const user = sessionData.user;

    // Find the selected channel from session data
    const selectedChannel = sessionData.channels.find((ch: any) => ch.id === channelId); // eslint-disable-line @typescript-eslint/no-explicit-any
    if (!selectedChannel) {
      return NextResponse.json({ error: "Channel not found" }, { status: 404 });
    }

    // Save YouTube connection to Firestore
    const connectionData = {
      userId: user.id,
      userEmail: user.email,
      channelId: channelId,
      channelName: selectedChannel.title,
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token || null,
      expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
      connectedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Best-effort save; don't fail the flow if Firestore isn't configured
    try {
      await adminDb.collection('youtubeConnections').doc(channelId).set(connectionData);
      // Persist user's default/selected channel on users/{uid}
      await adminDb.collection('users').doc(user.id).set({
        selectedChannelId: channelId,
        selectedChannelName: selectedChannel.title,
        updatedAt: new Date().toISOString()
      }, { merge: true });
    } catch (e) {
      console.warn("Skipping Firestore save (unconfigured):", (e as Error).message);
    }

    // Also update session cookie for short-term continuity, but primary source is Firestore now
    const updatedSessionData = {
      ...sessionData,
      selectedChannelId: channelId,
      selectedChannelName: selectedChannel.title
    };

    const updatedSessionToken = Buffer.from(JSON.stringify(updatedSessionData)).toString('base64');

    const response = NextResponse.json({
      message: "Channel connected successfully",
      channelId,
      channelName: selectedChannel.title
    });

    response.cookies.set('youtube_session', updatedSessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24, // 24 hours
      path: '/',
    });

    return response;
  } catch (error) {
    console.error("Error connecting YouTube channel:", error);
    return NextResponse.json({ error: "Failed to connect channel" }, { status: 500 });
  }
}
