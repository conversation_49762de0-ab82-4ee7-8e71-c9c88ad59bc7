import { NextRequest, NextResponse } from "next/server";
import { google } from 'googleapis';
import { adminDb, adminAuth } from "@/lib/firebase/admin";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const origin = process.env.NEXTAUTH_URL || `${url.protocol}//${url.host}`;

    // Build OAuth client with the actual origin to avoid redirect_uri mismatches
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${origin}/api/auth/google/callback`
    );

    const { searchParams } = url;
    const code = searchParams.get('code') || undefined;
    const stateRaw = searchParams.get('state') || undefined; // Encoded path to continue flow
    const state = stateRaw ? decodeURIComponent(stateRaw) : undefined;
    const error = searchParams.get('error') || undefined;

    if (error) {
      console.error('OAuth error:', error);
      return NextResponse.redirect(`${origin}/auth/error?error=${encodeURIComponent(error)}`);
    }

    if (!code) {
      return NextResponse.redirect(`${origin}/auth/error?error=no_code`);
    }

    // Exchange code for tokens
    const codeStr = code as string; // after guard above, safe
    const { tokens } = await oauth2Client.getToken(codeStr);
    oauth2Client.setCredentials(tokens);

    // Get user info
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const { data: userInfo } = await oauth2.userinfo.get();

    // Get YouTube channels
    const youtube = google.youtube({ version: 'v3', auth: oauth2Client });
    const channelsResponse = await youtube.channels.list({
      part: ['snippet', 'statistics'],
      mine: true,
    });

    const channels = channelsResponse.data.items || [];

    // Store user and YouTube connection info in session/cookie
    const sessionData = {
      user: {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
      },
      tokens: {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expiry_date: tokens.expiry_date,
      },
      channels: channels.map(channel => ({
        id: channel.id,
        title: channel.snippet?.title,
        thumbnail: channel.snippet?.thumbnails?.default?.url,
        subscriberCount: channel.statistics?.subscriberCount,
        videoCount: channel.statistics?.videoCount,
      }))
    };

    // Create Firebase Auth user and users collection entry
    let firebaseUid = userInfo.id;
    try {
      // Try to get existing user
      await adminAuth.getUser(firebaseUid);
    } catch {
      // Create new Firebase Auth user if doesn't exist
      try {
        await adminAuth.createUser({
          uid: firebaseUid,
          email: userInfo.email,
          displayName: userInfo.name,
          photoURL: userInfo.picture,
        });
      } catch (createError) {
        console.error('Firebase user creation failed:', createError);
      }
    }

    // Upsert users collection document
    try {
      await adminDb.collection('users').doc(firebaseUid).set({
        uid: firebaseUid,
        email: userInfo.email,
        name: userInfo.name,
        photoURL: userInfo.picture,
        packCredits: 0,
        currentPack: null,
        hasMemoryAccess: false,
        isFirstTimer: true,
        lastLoginAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }, { merge: true });
    } catch (firestoreError) {
      console.warn('Firestore users upsert failed:', firestoreError);
    }

    // Create a secure session token (you might want to use JWT here)
    const sessionToken = Buffer.from(JSON.stringify(sessionData)).toString('base64');

    // Build absolute redirect target
    const target = state ?? '/auth/select-channel';
    const redirectTarget = target.startsWith('http') ? target : `${origin}${target}`;

    // Redirect to channel selection with session data
    const response = NextResponse.redirect(redirectTarget);

    // Set cookie domain to cover both apex and www
    const host = url.host.split(':')[0];
    const baseDomain = host.startsWith('www.') ? host.slice(4) : host;
    const cookieDomain = host.includes('localhost') ? undefined : `.${baseDomain}`;

    response.cookies.set('youtube_session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24, // 24 hours
      path: '/',
      ...(cookieDomain ? { domain: cookieDomain } : {}),
    });

    return response;
  } catch (error: any) {
    console.error('OAuth callback error:', error?.response?.data || error?.message || error);
    const origin = process.env.NEXTAUTH_URL || '';
    const reason = encodeURIComponent(error?.message || 'unknown');
    return NextResponse.redirect(`${origin || '/auth/error'}?error=callback_failed&reason=${reason}`);
  }
}
