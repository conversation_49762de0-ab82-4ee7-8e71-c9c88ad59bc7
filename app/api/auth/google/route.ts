import { NextRequest, NextResponse } from "next/server";
import { google } from 'googleapis';

export async function GET(req: NextRequest) {
  try {
    // Check if Google OAuth credentials are configured
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      console.error('Google OAuth credentials not configured');
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/auth/error?error=oauth_not_configured`);
    }

    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/auth/google/callback`
    );

    const { searchParams } = new URL(req.url);
    const callbackUrl = searchParams.get('callbackUrl') || '/';

    // Generate the URL for Google OAuth with YouTube scopes
    const scopes = [
      'openid',
      'email',
      'profile',
      'https://www.googleapis.com/auth/youtube.readonly',
      'https://www.googleapis.com/auth/yt-analytics.readonly'
    ];

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      state: callbackUrl, // Pass callback URL in state
      include_granted_scopes: true // Respect previously granted scopes; avoid showing consent every time
      // prompt: 'consent' // Uncomment only when you explicitly need to force re-consent
    });

    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Google OAuth error:', error);
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/auth/error?error=oauth_failed`);
  }
}
