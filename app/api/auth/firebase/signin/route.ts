import { NextRequest, NextResponse } from "next/server";
import { auth } from "firebase-admin";

export async function POST(req: NextRequest) {
  try {
    const { customToken } = await req.json();
    if (!customToken) return NextResponse.json({ error: "no_token" }, { status: 400 });
    // This endpoint is just a placeholder in case we need server mediation later
    return NextResponse.json({ ok: true });
  } catch (e) {
    return NextResponse.json({ error: "signin_failed" }, { status: 500 });
  }
}

