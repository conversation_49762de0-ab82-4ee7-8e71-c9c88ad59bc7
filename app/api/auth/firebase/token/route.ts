import { NextRequest, NextResponse } from "next/server";
import { adminAuth, adminDb } from "@/lib/firebase/admin";

export async function GET(req: NextRequest) {
  try {
    const sessionCookie = req.cookies.get("youtube_session");
    if (!sessionCookie) {
      return NextResponse.json({ error: "No session" }, { status: 401 });
    }

    const session = JSON.parse(Buffer.from(sessionCookie.value, "base64").toString());
    const user = session?.user;
    if (!user?.id || !user?.email) {
      return NextResponse.json({ error: "Invalid session" }, { status: 400 });
    }

    // Ensure Firebase Auth user exists
    const uid = user.id;
    let isNewUser = false;
    try {
      await adminAuth.getUser(uid);
      console.log("Firebase user exists:", uid);
    } catch {
      console.log("Creating new Firebase user:", uid);
      await adminAuth.createUser({
        uid,
        email: user.email,
        displayName: user.name,
        photoURL: user.picture,
      });
      isNewUser = true;
    }

    // Upsert users collection (best-effort)
    try {
      await adminDb.collection("users").doc(uid).set(
        {
          uid,
          email: user.email,
          name: user.name,
          photoURL: user.picture,
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
        },
        { merge: true }
      );
    } catch (e) {
      console.warn("users doc upsert failed:", (e as Error).message);
    }

    const customToken = await adminAuth.createCustomToken(uid);
    return NextResponse.json({ customToken });
  } catch (error) {
    console.error("firebase token error:", error);
    return NextResponse.json({ error: "token_failed" }, { status: 500 });
  }
}

