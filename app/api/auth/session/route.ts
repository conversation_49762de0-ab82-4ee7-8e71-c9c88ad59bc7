import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    const sessionCookie = req.cookies.get('youtube_session');
    
    if (!sessionCookie) {
      return NextResponse.json({ error: "No session found" }, { status: 401 });
    }

    // Decode session data
    const sessionData = JSON.parse(Buffer.from(sessionCookie.value, 'base64').toString());
    
    // Check if tokens are expired
    const now = Date.now();
    if (sessionData.tokens.expiry_date && sessionData.tokens.expiry_date < now) {
      // TODO: Implement token refresh logic here
      return NextResponse.json({ error: "Session expired" }, { status: 401 });
    }

    return NextResponse.json(sessionData);
  } catch (error) {
    console.error("Session error:", error);
    return NextResponse.json({ error: "Invalid session" }, { status: 401 });
  }
}
