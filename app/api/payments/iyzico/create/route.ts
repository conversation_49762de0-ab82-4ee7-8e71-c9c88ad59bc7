import { NextRequest, NextResponse } from "next/server";
import { getIyzico } from "@/lib/iyzico";
import dayjs from "dayjs";
import { adminAuth } from "@/lib/firebase/admin";
import { convertUsdToTry } from "@/lib/tcmb";
import { cookies } from "next/headers";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";
async function verify(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;
  const token = authHeader.split("Bearer ")[1];
  if (!token) return null;
  try {
    const decoded = await adminAuth.verifyIdToken(token);
    return decoded.uid as string;
  } catch {
    return null;
  }
}


const PLAN_TO_PRICE = {
  first: { price: 2.00, credits: 1, memory: false },
  single: { price: 4.99, credits: 1, memory: false },
  creator: { price: 19.99, credits: 5, memory: true },
  pro: { price: 34.99, credits: 10, memory: true },
} as const;

export async function POST(req: NextRequest) {
  const requestId = `payment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[iyzico/create] ${requestId} - Starting payment creation`);

    const { plan = "single", channelId, queryId, billing } = await req.json();
    console.log(`[iyzico/create] ${requestId} - Request data:`, { plan, channelId, queryId, billingExists: !!billing });

    let uid = (await verify(req)) || null;
    if (!uid) {
      try {
        const sessionCookie = cookies().get("youtube_session");
        if (sessionCookie?.value) {
          const session = JSON.parse(Buffer.from(sessionCookie.value, "base64").toString());
          uid = session?.user?.id || null;
          console.log(`[iyzico/create] ${requestId} - User ID from session:`, uid);
        }
      } catch (e) {
        console.warn(`[iyzico/create] ${requestId} - Session parse failed:`, (e as Error).message);
        await ErrorHandler.logError(e as Error, {
          userId: uid || "unknown",
          endpoint: "/api/payments/iyzico/create",
          method: "POST"
        }, "medium");
      }
    }
    uid = uid || "guest";
    console.log(`[iyzico/create] ${requestId} - Final user ID:`, uid);

    if (!Object.keys(PLAN_TO_PRICE).includes(plan)) {
      console.error(`[iyzico/create] ${requestId} - Invalid plan:`, plan);
      await ErrorHandler.logError(`Invalid payment plan: ${plan}`, {
        userId: uid,
        endpoint: "/api/payments/iyzico/create",
        method: "POST"
      }, "medium");
      return NextResponse.json({ error: "Geçersiz plan" }, { status: 400 });
    }

    const basePriceUsd = PLAN_TO_PRICE[plan as keyof typeof PLAN_TO_PRICE].price;
    console.log(`[iyzico/create] ${requestId} - Plan pricing:`, { plan, basePriceUsd });

    const iyzico = getIyzico();
    console.log(`[iyzico/create] ${requestId} - Iyzico client initialized`);

    const isSandbox = (process.env.IYZICO_BASE_URL || "").includes("sandbox");
    const checkoutHost = isSandbox ? "https://sandbox-iyzipay.iyzipay.com" : "https://secure.iyzipay.com";
    console.log(`[iyzico/create] ${requestId} - Environment:`, { isSandbox, checkoutHost });

    // Determine currency and final price based on customer country
    const isTurkishCustomer = billing?.country === "TR";
    let finalPrice: number;
    let currency: string;
    let usedFxRate: number | null = null;

    if (isTurkishCustomer) {
      console.log(`[iyzico/create] ${requestId} - Turkish customer detected, converting ${basePriceUsd} USD to TRY`);
      try {
        const conversion = await convertUsdToTry(basePriceUsd);
        finalPrice = conversion.tryAmount;
        currency = "TRY";
        usedFxRate = conversion.rate;
        console.log(`[iyzico/create] ${requestId} - Currency conversion successful:`, { finalPrice, usedFxRate });
      } catch (e) {
        console.error(`[iyzico/create] ${requestId} - Currency conversion failed:`, (e as Error).message);
        await ErrorHandler.logError(e as Error, {
          userId: uid,
          endpoint: "/api/payments/iyzico/create",
          method: "POST"
        }, "high");
        // Fallback to USD
        finalPrice = basePriceUsd;
        currency = "USD";
        console.log(`[iyzico/create] ${requestId} - Falling back to USD:`, finalPrice);
      }
    } else {
      finalPrice = basePriceUsd;
      currency = "USD";
      console.log(`[iyzico/create] ${requestId} - Non-Turkish customer, using ${finalPrice} USD`);
    }

    const initResult: { token: string; checkoutFormContent?: string } = await new Promise((resolve, reject) => {
      const initializeRequest = {
        locale: "EN",
        price: finalPrice.toFixed(2),
        paidPrice: finalPrice.toFixed(2),
        currency: currency,
        basketId: `basket_${plan}_${Date.now()}`,
        paymentGroup: "PRODUCT",
        callbackUrl: `${process.env.APP_BASE_URL}/api/payments/iyzico/callback?queryId=${encodeURIComponent(queryId || "")}&channelId=${encodeURIComponent(channelId || "")}&uid=${encodeURIComponent(uid as string)}`,
        // debug context (not sent to Iyzico)
        _debug: {
          appBaseUrl: process.env.APP_BASE_URL,
          currency: currency,
          basePriceUsd: basePriceUsd,
          finalPrice: finalPrice,
          usedFxRate: usedFxRate,
        },
        buyer: {
          id: uid,
          name: (billing?.fullName || "Guest").split(" ")[0] || "Guest",
          surname: (billing?.fullName || "User").split(" ").slice(1).join(" ") || "User",
          gsmNumber: billing?.phone || "+************",
          email: billing?.email || "<EMAIL>",
          identityNumber: billing?.country === "TR" ? (billing?.nationalId || billing?.taxId || "11111111110") : undefined,
          lastLoginDate: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          registrationDate: dayjs().format("YYYY-MM-DD HH:mm:ss"),
          registrationAddress: `${billing?.address1 || ""} ${billing?.address2 || ""}`.trim(),
          ip: "127.0.0.1",
          city: billing?.city || "",
          country: billing?.country || "TR",
          zipCode: billing?.postalCode || "",
        },
        shippingAddress: {
          contactName: billing?.fullName || "Guest User",
          city: billing?.city || "",
          country: billing?.country || "TR",
          address: `${billing?.address1 || ""} ${billing?.address2 || ""}`.trim(),
          zipCode: billing?.postalCode || "",
        },
        billingAddress: {
          contactName: billing?.isBusiness ? (billing?.companyName || billing?.fullName || "Guest User") : (billing?.fullName || "Guest User"),
          city: billing?.city || "",
          country: billing?.country || "TR",
          address: `${billing?.address1 || ""} ${billing?.address2 || ""}`.trim(),
          zipCode: billing?.postalCode || "",
        },
        basketItems: [
          {
            id: plan,
            name: `Ytuber ${plan}`,
            category1: "Digital",
            itemType: "VIRTUAL",
            price: finalPrice.toFixed(2),
          },
        ],
      } as unknown as Record<string, unknown>;

      console.log(`[iyzico/create] ${requestId} - Sending request to Iyzico:`, {
        basketId: initializeRequest.basketId,
        price: initializeRequest.price,
        currency: initializeRequest.currency,
        buyerEmail: initializeRequest.buyer.email
      });

      (iyzico as unknown as { checkoutFormInitialize: { create: (
        req: Record<string, unknown>,
        cb: (err: unknown, result: unknown) => void
      ) => void } }).checkoutFormInitialize.create(
        initializeRequest,
        (err: unknown, result: unknown) => {
          if (err) {
            console.error(`[iyzico/create] ${requestId} - Iyzico API error:`, err);
            ErrorHandler.logError(err as Error, {
              userId: uid,
              endpoint: "/api/payments/iyzico/create",
              method: "POST"
            }, "high").catch(console.error);
            return reject(err);
          }
          const r = result as { token?: string; checkoutFormContent?: string; paymentPageUrl?: string };
          console.log(`[iyzico/create] ${requestId} - Iyzico response:`, {
            hasToken: !!r?.token,
            htmlLen: (r as any)?.checkoutFormContent?.length,
            paymentPageUrl: (r as any)?.paymentPageUrl
          });

          if (!r?.token) {
            const error = new Error("Iyzico token alınamadı");
            console.error(`[iyzico/create] ${requestId} - No token received from Iyzico`);
            ErrorHandler.logError(error, {
              userId: uid,
              endpoint: "/api/payments/iyzico/create",
              method: "POST"
            }, "high").catch(console.error);
            return reject(error);
          }

          console.log(`[iyzico/create] ${requestId} - Payment initialization successful`);
          resolve({ token: r.token, checkoutFormContent: (r as any).checkoutFormContent, paymentPageUrl: (r as any)?.paymentPageUrl });
        }
      );
    });

    console.log(`[iyzico/create] ${requestId} - Returning payment response`);
    return NextResponse.json({
      ...initResult,
      checkoutHost,
      currency,
      usedFxRate,
      finalPrice: finalPrice.toFixed(2)
    });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Hata";
    console.error(`[iyzico/create] ${requestId} - Unexpected error:`, message);
    await ErrorHandler.logError(e as Error, {
      userId: uid || "unknown",
      endpoint: "/api/payments/iyzico/create",
      method: "POST"
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


