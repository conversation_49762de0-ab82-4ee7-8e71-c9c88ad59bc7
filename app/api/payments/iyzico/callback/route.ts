import { NextRequest, NextResponse } from "next/server";
import { getIyzico } from "@/lib/iyzico";
import { adminDb } from "@/lib/firebase/admin";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";
import { addPackToUser, PACK_CONFIGS } from "@/lib/pack-system";

type IyzicoCheckoutRetrieve = {
  paymentStatus?: "SUCCESS" | "FAILURE";
  basketId?: string;
  buyer?: { id?: string };
  paymentId?: string;
};

export async function POST(req: NextRequest) {
  const callbackId = `callback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[iyzico/callback] ${callbackId} - Processing payment callback`);

    const form = await req.formData();
    const token = form.get("token") as string;
    console.log(`[iyzico/callback] ${callbackId} - Token received:`, token ? "present" : "missing");

    if (!token) {
      console.error(`[iyzico/callback] ${callbackId} - No token provided`);
      await <PERSON>rror<PERSON>andler.logError("Payment callback received without token", {
        endpoint: "/api/payments/iyzico/callback",
        method: "POST"
      }, "high");
      return NextResponse.json({ error: "Token required" }, { status: 400 });
    }

    const iyzico = getIyzico();
    console.log(`[iyzico/callback] ${callbackId} - Iyzico client initialized`);

    const result: IyzicoCheckoutRetrieve = await new Promise((resolve, reject) => {
      console.log(`[iyzico/callback] ${callbackId} - Retrieving payment details from Iyzico`);
      iyzico.checkoutForm.retrieve({ locale: "TR", token }, (err: unknown, res: unknown) => {
        if (err) {
          console.error(`[iyzico/callback] ${callbackId} - Iyzico retrieve error:`, err);
          ErrorHandler.logError(err as Error, {
            endpoint: "/api/payments/iyzico/callback",
            method: "POST"
          }, "high").catch(console.error);
          return reject(err);
        }
        console.log(`[iyzico/callback] ${callbackId} - Iyzico response:`, {
          paymentStatus: (res as any)?.paymentStatus,
          paymentId: (res as any)?.paymentId,
          basketId: (res as any)?.basketId
        });
        resolve(res as IyzicoCheckoutRetrieve);
      });
    });

    const paymentStatus = result?.paymentStatus; // SUCCESS or FAILURE
    const basketId = result?.basketId as string | undefined;
    console.log(`[iyzico/callback] ${callbackId} - Payment status:`, paymentStatus);

    // Prefer uid from query string (we pass it from create), fall back to iyzico buyer.id
    const urlParams = new URLSearchParams(req.url.split('?')[1] || '');
    const uidFromQuery = urlParams.get('uid');
    const uid = uidFromQuery || result?.buyer?.id || "guest";
    console.log(`[iyzico/callback] ${callbackId} - User ID:`, uid);

    // Extract tracking info from basketId or other sources
    const queryId = urlParams.get('queryId');
    const channelId = urlParams.get('channelId');
    console.log(`[iyzico/callback] ${callbackId} - Tracking info:`, { queryId, channelId });

    const purchaseRef = adminDb.collection("purchases").doc(token);
    const existing = await purchaseRef.get();
    console.log(`[iyzico/callback] ${callbackId} - Purchase record exists:`, existing.exists);

    if (!existing.exists) {
      // Determine pack type from basketId
      let packType = "single" as keyof typeof PACK_CONFIGS;
      if (basketId?.includes("first")) packType = "first";
      else if (basketId?.includes("creator")) packType = "creator";
      else if (basketId?.includes("pro")) packType = "pro";
      console.log(`[iyzico/callback] ${callbackId} - Determined pack type:`, packType);

      console.log(`[iyzico/callback] ${callbackId} - Creating purchase record`);
      await purchaseRef.set({
        uid,
        type: packType,
        iyzicoPaymentId: result?.paymentId ?? null,
        status: paymentStatus === "SUCCESS" ? "paid" : "failed",
        createdAt: new Date(),
      });

      if (paymentStatus === "SUCCESS") {
        console.log(`[iyzico/callback] ${callbackId} - Payment successful, adding pack to user`);
        // Add pack to user using new pack system
        try {
          await addPackToUser(uid, packType, result?.paymentId);
          console.log(`[iyzico/callback] ${callbackId} - Pack added successfully via pack system`);
        } catch (error) {
          console.error(`[iyzico/callback] ${callbackId} - Pack system failed, using Firestore fallback:`, (error as Error).message);
          await ErrorHandler.logError(error as Error, {
            userId: uid,
            endpoint: "/api/payments/iyzico/callback",
            method: "POST"
          }, "medium");

          // Fallback to Firestore user doc – keep both legacy "credits" and new "packCredits" in sync
          const userRef = adminDb.collection("users").doc(uid);
          const packCfg = PACK_CONFIGS[packType] ?? { credits: 1, hasMemoryAccess: false } as any;
          await adminDb.runTransaction(async (tx) => {
            const snap = await tx.get(userRef);
            const data = snap.exists ? snap.data()! : {};
            const legacyCredits = (data.credits ?? 0) + packCfg.credits;
            const packCredits = (data.packCredits ?? 0) + packCfg.credits;
            console.log(`[iyzico/callback] ${callbackId} - Updating user credits:`, { legacyCredits, packCredits });
            tx.set(userRef, {
              credits: legacyCredits,
              packCredits,
              currentPack: packType,
              hasMemoryAccess: !!packCfg.hasMemoryAccess,
              isFirstTimer: packType === 'first' ? false : (data.isFirstTimer ?? true),
              email: data.email ?? null,
              updatedAt: new Date().toISOString()
            }, { merge: true });
          });
          console.log(`[iyzico/callback] ${callbackId} - Firestore fallback completed`);
        }
      } else {
        console.log(`[iyzico/callback] ${callbackId} - Payment failed, not adding pack`);
      }

      // Track payment completion
      if (queryId) {
        console.log(`[iyzico/callback] ${callbackId} - Tracking payment completion for query:`, queryId);
        try {
          await adminDb.collection('urlQueries').doc(queryId).update({
            paymentCompleted: true,
            paymentAmount: parseFloat(basketId?.split('_')[1] || '0'),
            reportType: basketId?.includes('pack') ? 'premium' : 'basic',
            updatedAt: new Date().toISOString()
          });
          console.log(`[iyzico/callback] ${callbackId} - Payment tracking updated successfully`);
        } catch (error) {
          console.error(`[iyzico/callback] ${callbackId} - Error tracking payment completion:`, error);
          await ErrorHandler.logError(error as Error, {
            userId: uid,
            endpoint: "/api/payments/iyzico/callback",
            method: "POST"
          }, "medium");
        }
      }
    } else {
      console.log(`[iyzico/callback] ${callbackId} - Purchase record already exists, skipping processing`);
    }

    const redirectPath = paymentStatus === "SUCCESS" ? "/dashboard?payment=success" : "/dashboard?payment=failed";
    const target = new URL(redirectPath, process.env.APP_BASE_URL);
    console.log(`[iyzico/callback] ${callbackId} - Redirecting to:`, redirectPath);

    // Return HTML that redirects the TOP window out of the iframe. This works regardless of iframe sandboxing.
    const html = `<!doctype html><html><head><meta charset="utf-8" />
      <meta http-equiv="refresh" content="0;url=${target.toString()}" />
    </head><body>
      <script>try { window.top.location.href = ${JSON.stringify(target.toString())}; } catch (e) { window.location.href = ${JSON.stringify(target.toString())}; }</script>
      <noscript>
        <a href="${target.toString()}">Continue</a>
      </noscript>
    </body></html>`;

    console.log(`[iyzico/callback] ${callbackId} - Payment callback processing completed successfully`);
    return new NextResponse(html, { status: 200, headers: { "Content-Type": "text/html; charset=utf-8" } });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Hata";
    console.error(`[iyzico/callback] ${callbackId} - Unexpected error:`, message);
    await ErrorHandler.logError(e as Error, {
      userId: uid || "unknown",
      endpoint: "/api/payments/iyzico/callback",
      method: "POST"
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


