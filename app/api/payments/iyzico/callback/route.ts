import { NextRequest, NextResponse } from "next/server";
import { getIyzico } from "@/lib/iyzico";
import { adminDb } from "@/lib/firebase/admin";
import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";
import { addPackToUser, PACK_CONFIGS } from "@/lib/pack-system";

type IyzicoCheckoutRetrieve = {
  paymentStatus?: "SUCCESS" | "FAILURE";
  basketId?: string;
  buyer?: { id?: string };
  paymentId?: string;
};

export async function POST(req: NextRequest) {
  try {
    const form = await req.formData();
    const token = form.get("token") as string;
    const iyzico = getIyzico();

    const result: IyzicoCheckoutRetrieve = await new Promise((resolve, reject) => {
      iyzico.checkoutForm.retrieve({ locale: "TR", token }, (err: unknown, res: unknown) => {
        if (err) {
          console.error("[iyzico/callback] retrieve error", err);
          return reject(err);
        }
        console.log("[iyzico/callback] retrieve result", { paymentStatus: (res as any)?.paymentStatus, paymentId: (res as any)?.paymentId });
        resolve(res as IyzicoCheckoutRetrieve);
      });
    });

    const paymentStatus = result?.paymentStatus; // SUCCESS or FAILURE
    const basketId = result?.basketId as string | undefined;
    // Prefer uid from query string (we pass it from create), fall back to iyzico buyer.id
    const urlParams = new URLSearchParams(req.url.split('?')[1] || '');
    const uidFromQuery = urlParams.get('uid');
    const uid = uidFromQuery || result?.buyer?.id || "guest";

    // Extract tracking info from basketId or other sources
    // Note: In a real implementation, you'd want to store this info more securely
    const queryId = urlParams.get('queryId');
    const channelId = urlParams.get('channelId');

    const purchaseRef = adminDb.collection("purchases").doc(token);
    const existing = await purchaseRef.get();
    if (!existing.exists) {
      // Determine pack type from basketId
      let packType = "single" as keyof typeof PACK_CONFIGS;
      if (basketId?.includes("first")) packType = "first";
      else if (basketId?.includes("creator")) packType = "creator";
      else if (basketId?.includes("pro")) packType = "pro";

      await purchaseRef.set({
        uid,
        type: packType,
        iyzicoPaymentId: result?.paymentId ?? null,
        status: paymentStatus === "SUCCESS" ? "paid" : "failed",
        createdAt: new Date(),
      });
      if (paymentStatus === "SUCCESS") {
        // Add pack to user using new pack system
        try {
          await addPackToUser(uid, packType, result?.paymentId);
        } catch (error) {
          console.error('Error adding pack to user (prisma fallback to Firestore):', (error as Error).message);
          // Fallback to Firestore user doc – keep both legacy "credits" and new "packCredits" in sync
          const userRef = adminDb.collection("users").doc(uid);
          const packCfg = PACK_CONFIGS[packType] ?? { credits: 1, hasMemoryAccess: false } as any;
          await adminDb.runTransaction(async (tx) => {
            const snap = await tx.get(userRef);
            const data = snap.exists ? snap.data()! : {};
            const legacyCredits = (data.credits ?? 0) + packCfg.credits;
            const packCredits = (data.packCredits ?? 0) + packCfg.credits;
            tx.set(userRef, {
              credits: legacyCredits,
              packCredits,
              currentPack: packType,
              hasMemoryAccess: !!packCfg.hasMemoryAccess,
              isFirstTimer: packType === 'first' ? false : (data.isFirstTimer ?? true),
              email: data.email ?? null,
              updatedAt: new Date().toISOString()
            }, { merge: true });
          });
        }

        // Track payment completion
        if (queryId) {
          try {
            await adminDb.collection('urlQueries').doc(queryId).update({
              paymentCompleted: true,
              paymentAmount: parseFloat(basketId?.split('_')[1] || '0'),
              reportType: basketId?.includes('pack') ? 'premium' : 'basic',
              updatedAt: new Date().toISOString()
            });
          } catch (error) {
            console.error('Error tracking payment completion:', error);
          }
        }
      }
    }

    const redirectPath = paymentStatus === "SUCCESS" ? "/dashboard?payment=success" : "/dashboard?payment=failed";
    const target = new URL(redirectPath, process.env.APP_BASE_URL);
    // Return HTML that redirects the TOP window out of the iframe. This works regardless of iframe sandboxing.
    const html = `<!doctype html><html><head><meta charset="utf-8" />
      <meta http-equiv="refresh" content="0;url=${target.toString()}" />
    </head><body>
      <script>try { window.top.location.href = ${JSON.stringify(target.toString())}; } catch (e) { window.location.href = ${JSON.stringify(target.toString())}; }</script>
      <noscript>
        <a href="${target.toString()}">Continue</a>
      </noscript>
    </body></html>`;
    return new NextResponse(html, { status: 200, headers: { "Content-Type": "text/html; charset=utf-8" } });
  } catch (e: unknown) {
    const message = e instanceof Error ? e.message : "Hata";
    await ErrorHandler.logError(e as Error, {
      endpoint: "/api/payments/iyzico/callback",
      method: "POST",
    }, "high");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


