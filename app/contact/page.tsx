"use client";
import { motion } from "framer-motion";
import { Mail, MessageCircle, Clock, ArrowRight } from "lucide-react";
import Link from "next/link";

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full text-blue-400 text-sm font-medium border border-blue-500/30 mb-6">
              Get in Touch
            </span>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent">
                Contact
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                YTuber Support
              </span>
            </h1>
            <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
              Have questions about your YouTube analytics or need help with our platform? 
              We&apos;re here to help you grow your channel faster.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Options */}
      <section className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {/* Email Support */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10 hover:border-blue-500/30 transition-all duration-300"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                  <Mail className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">Email Support</h3>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Get personalized help with your YouTube analytics, growth strategies, 
                or technical issues. Our team responds within 24 hours.
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl group"
              >
                <Mail className="w-5 h-5 mr-2" />
                <EMAIL>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </a>
            </motion.div>

            {/* Priority Support */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-lg rounded-2xl p-8 border border-purple-500/30"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                  <Clock className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">Priority Support</h3>
                  <span className="text-purple-400 text-sm font-medium">Creator & Pro Pack Users</span>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Get VIP treatment with our premium support tiers. Fast 24-hour response times,
                dedicated growth consultations, and direct access to our YouTube experts.
              </p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  <span><strong className="text-purple-400">Creator Pack:</strong> Within 24 hours</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-pink-500 rounded-full mr-3"></div>
                  <span><strong className="text-pink-400">Pro Pack:</strong> Within 24 hours</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-gray-500 rounded-full mr-3"></div>
                  <span><strong className="text-gray-400">Other:</strong> Up to 5 business days</span>
                </div>
              </div>
              <Link
                href="/#pricing"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl group"
              >
                Upgrade for Priority Support
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </motion.div>
          </div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10"
          >
            <div className="flex items-center mb-8">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mr-4">
                <MessageCircle className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white">Common Questions</h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">How fast do you respond to support requests?</h4>
                  <p className="text-gray-300 text-sm">Response times vary by plan: Pro & Creator Pack (within 24 hours), Free users (up to 5 business days).</p>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">How accurate is the analysis?</h4>
                  <p className="text-gray-300 text-sm">Our AI analyzes real YouTube data with 95%+ accuracy using advanced algorithms.</p>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">How long does analysis take?</h4>
                  <p className="text-gray-300 text-sm">Most analyses complete within 2-5 minutes depending on channel size.</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">What&apos;s included in priority support?</h4>
                  <p className="text-gray-300 text-sm">Priority support includes faster response times, dedicated growth consultations, and direct expert access.</p>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Can I analyze multiple channels?</h4>
                  <p className="text-gray-300 text-sm">Yes! You can analyze as many channels as you need with our flexible plans.</p>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Do you offer refunds?</h4>
                  <p className="text-gray-300 text-sm">Refunds are only provided if analysis fails to generate or technical errors occur. We deliver your analysis or provide a refund.</p>
                </div>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-white/10">
              <p className="text-gray-300 text-center">
                Don&apos;t see your question here? 
                <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 ml-1 underline">
                  Email us directly
                </a>
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-12 border border-blue-500/20"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Grow Your Channel?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Get started with your YouTube analytics today and discover 
              what&apos;s holding your channel back from explosive growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Analyze Your Channel
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link
                href="/faq"
                className="inline-flex items-center px-8 py-4 bg-white/10 text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
              >
                View FAQ
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
