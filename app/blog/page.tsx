import { Metadata } from 'next';
import Link from 'next/link';
import { Calendar, Clock, ArrowRight, TrendingUp, Target, BarChart3 } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Growth Blog | Tips, Strategies & Analytics Insights - YTuber',
  description: 'Learn how to grow your YouTube channel with expert tips, growth strategies, and analytics insights. Free guides for YouTube creators.',
  keywords: 'YouTube growth tips, YouTube growth strategies, YouTube analytics, how to grow YouTube channel, YouTube growth blog, YouTube creator tips',
  openGraph: {
    title: 'YouTube Growth Blog | Tips, Strategies & Analytics Insights - YTuber',
    description: 'Learn how to grow your YouTube channel with expert tips, growth strategies, and analytics insights. Free guides for YouTube creators.',
    url: 'https://ytuber.life/blog',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'YouTube Growth Blog | Tips, Strategies & Analytics Insights - YTuber',
    description: 'Learn how to grow your YouTube channel with expert tips, growth strategies, and analytics insights. Free guides for YouTube creators.',
  },
  other: {
    canonical: 'https://ytuber.life/blog',
  },
};

const blogPosts = [
  {
    id: 'how-to-grow-youtube-channel-2025',
    title: 'How to Grow Your YouTube Channel in 2025: Complete Guide',
    excerpt: 'Discover the latest YouTube growth strategies that actually work. From algorithm optimization to content planning, learn everything you need to grow your channel.',
    date: '2025-01-15',
    readTime: '8 min read',
    category: 'Growth Strategies',
    icon: TrendingUp,
    featured: true
  },
  {
    id: 'youtube-analytics-explained',
    title: 'YouTube Analytics Explained: Key Metrics for Channel Growth',
    excerpt: 'Understanding your YouTube analytics is crucial for growth. Learn which metrics matter most and how to use them to optimize your content strategy.',
    date: '2025-01-12',
    readTime: '6 min read',
    category: 'Analytics',
    icon: BarChart3,
    featured: true
  },
  {
    id: 'youtube-algorithm-tips',
    title: 'YouTube Algorithm Tips: How to Get More Views and Subscribers',
    excerpt: 'Master the YouTube algorithm with these proven tips. Learn how to optimize your content for maximum visibility and engagement.',
    date: '2025-01-10',
    readTime: '7 min read',
    category: 'Algorithm',
    icon: Target,
    featured: false
  },
  {
    id: 'youtube-thumbnail-optimization',
    title: 'YouTube Thumbnail Optimization: Boost Your Click-Through Rate',
    excerpt: 'Your thumbnail is the first thing viewers see. Learn how to create compelling thumbnails that increase clicks and drive channel growth.',
    date: '2025-01-08',
    readTime: '5 min read',
    category: 'Optimization',
    icon: TrendingUp,
    featured: false
  },
  {
    id: 'youtube-growth-tools',
    title: 'Best YouTube Growth Tools for Content Creators in 2025',
    excerpt: 'Discover the essential tools every YouTube creator needs to grow their channel. From analytics to optimization, find the right tools for your growth journey.',
    date: '2025-01-05',
    readTime: '10 min read',
    category: 'Tools',
    icon: BarChart3,
    featured: false
  },
  {
    id: 'youtube-content-strategy',
    title: 'YouTube Content Strategy: Planning for Consistent Growth',
    excerpt: 'Build a winning content strategy that drives consistent YouTube growth. Learn how to plan, create, and optimize content that resonates with your audience.',
    date: '2025-01-03',
    readTime: '9 min read',
    category: 'Strategy',
    icon: Target,
    featured: false
  }
];

export default function BlogPage() {
  const featuredPosts = blogPosts.filter(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'Blog', href: '/blog' }]}
            className="mb-8"
          />

          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-5xl font-bold text-white mb-6">
              YouTube Growth Blog
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Learn how to grow your YouTube channel with expert tips, proven strategies, 
              and actionable insights from successful creators and analytics experts.
            </p>
          </div>

          {/* Featured Posts */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-white mb-8">Featured Articles</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {featuredPosts.map((post) => {
                const IconComponent = post.icon;
                return (
                  <Link 
                    key={post.id} 
                    href={`/blog/${post.id}`}
                    className="group"
                  >
                    <article className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 h-full hover:bg-white/15 transition-all duration-300 border border-white/10">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-5 h-5 text-white" />
                        </div>
                        <span className="text-blue-400 font-medium">{post.category}</span>
                      </div>
                      
                      <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">
                        {post.title}
                      </h3>
                      
                      <p className="text-gray-300 mb-6 leading-relaxed">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between text-sm text-gray-400">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(post.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{post.readTime}</span>
                          </div>
                        </div>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </article>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* All Posts */}
          <div>
            <h2 className="text-3xl font-bold text-white mb-8">All Articles</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularPosts.map((post) => {
                const IconComponent = post.icon;
                return (
                  <Link 
                    key={post.id} 
                    href={`/blog/${post.id}`}
                    className="group"
                  >
                    <article className="bg-white/5 backdrop-blur-sm rounded-xl p-6 h-full hover:bg-white/10 transition-all duration-300 border border-white/5">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-blue-400 text-sm font-medium">{post.category}</span>
                      </div>
                      
                      <h3 className="text-lg font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                        {post.title}
                      </h3>
                      
                      <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <div className="flex items-center gap-3">
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                          <span>{post.readTime}</span>
                        </div>
                        <ArrowRight className="w-3 h-3 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </article>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-20 text-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Grow Your YouTube Channel?
              </h2>
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
                Get detailed analytics and growth insights for your channel. 
                Discover what's working and what needs improvement.
              </p>
              <Link
                href="/"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Analyze Your Channel
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
