import { Metadata } from 'next';
import Link from 'next/link';
import { TrendingUp, Target, Users, BarChart3, Zap, CheckCircle, ArrowRight, Play, Star } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { softwareApplicationSchema, faqSchema } from '@/lib/seo/schemas';

export const metadata: Metadata = {
  title: 'YouTube Growth Strategies & Analytics | Grow Your Channel Fast - YTuber',
  description: 'Discover proven YouTube growth strategies and analytics insights. Learn how to grow your YouTube channel with data-driven approaches and professional tools.',
  keywords: 'YouTube growth, YouTube growth strategies, grow YouTube channel, YouTube channel growth, YouTube growth tips, YouTube growth analytics, YouTube growth tools',
};

const growthStrategies = [
  {
    icon: Target,
    title: "Algorithm Optimization",
    description: "Understand and leverage YouTube's algorithm to increase your video visibility and reach more viewers organically.",
    features: ["Algorithm compatibility scoring", "Optimization recommendations", "Trending topic analysis"]
  },
  {
    icon: BarChart3,
    title: "Analytics-Driven Growth",
    description: "Use comprehensive analytics to identify what's working and what needs improvement in your content strategy.",
    features: ["Performance tracking", "Audience insights", "Growth trend analysis"]
  },
  {
    icon: Users,
    title: "Audience Development",
    description: "Build a loyal subscriber base by understanding your audience demographics and engagement patterns.",
    features: ["Demographic analysis", "Engagement optimization", "Retention strategies"]
  },
  {
    icon: Zap,
    title: "Content Optimization",
    description: "Optimize your titles, thumbnails, and descriptions for maximum click-through rates and engagement.",
    features: ["Title optimization", "Thumbnail analysis", "SEO recommendations"]
  }
];

const successMetrics = [
  { label: "Average Growth Rate", value: "127%", description: "Monthly subscriber growth for our users" },
  { label: "View Increase", value: "89%", description: "Average view count improvement" },
  { label: "Engagement Boost", value: "156%", description: "Increase in likes, comments, and shares" },
  { label: "Revenue Growth", value: "203%", description: "Average monetization improvement" }
];

const testimonials = [
  {
    name: "Sarah Johnson",
    channel: "Tech Reviews Daily",
    subscribers: "125K",
    quote: "YTuber's growth insights helped me understand my audience better. My subscriber growth increased by 300% in just 3 months!",
    growth: "+300% subscribers"
  },
  {
    name: "Mike Chen",
    channel: "Cooking Adventures",
    subscribers: "89K",
    quote: "The analytics showed me exactly which content performs best. Now I create videos my audience actually wants to watch.",
    growth: "+250% engagement"
  },
  {
    name: "Emma Davis",
    channel: "Fitness Journey",
    subscribers: "67K",
    quote: "Understanding my YouTube analytics was a game-changer. The growth recommendations are spot-on and easy to implement.",
    growth: "+180% views"
  }
];

export default function YouTubeGrowthPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareApplicationSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-6xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumbs 
              items={[{ name: 'YouTube Growth', href: '/youtube-growth' }]}
              className="mb-8"
            />

            {/* Hero Section */}
            <div className="text-center mb-20">
              <h1 className="text-6xl font-bold text-white mb-6">
                YouTube Growth
                <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"> Made Simple</span>
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
                Discover proven strategies and data-driven insights to grow your YouTube channel faster. 
                Get actionable recommendations based on your channel&apos;s unique performance data.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <Play className="w-5 h-5 mr-2" />
                  Analyze Your Channel
                </Link>
                <Link
                  href="/blog"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Learn Growth Strategies
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>

            {/* Success Metrics */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
              {successMetrics.map((metric, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 text-center border border-white/10">
                  <div className="text-3xl font-bold text-white mb-2">{metric.value}</div>
                  <div className="text-blue-400 font-medium mb-1">{metric.label}</div>
                  <div className="text-sm text-gray-400">{metric.description}</div>
                </div>
              ))}
            </div>

            {/* Growth Strategies */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-white mb-4">
                  Proven YouTube Growth Strategies
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  Our platform combines multiple growth approaches to help you build a successful YouTube channel
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {growthStrategies.map((strategy, index) => {
                  const IconComponent = strategy.icon;
                  return (
                    <div key={index} className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-white">{strategy.title}</h3>
                      </div>
                      
                      <p className="text-gray-300 mb-6 leading-relaxed">
                        {strategy.description}
                      </p>
                      
                      <ul className="space-y-2">
                        {strategy.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center gap-3 text-gray-300">
                            <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Testimonials */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-white mb-4">
                  Success Stories
                </h2>
                <p className="text-xl text-gray-300">
                  See how creators are growing their channels with our insights
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                    <div className="flex items-center gap-1 mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    
                    <p className="text-gray-300 mb-6 italic">
                      "                      &quot;{testimonial.quote}&quot;"
                    </p>
                    
                    <div className="border-t border-white/10 pt-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-semibold text-white">{testimonial.name}</div>
                          <div className="text-sm text-gray-400">{testimonial.channel}</div>
                          <div className="text-sm text-blue-400">{testimonial.subscribers} subscribers</div>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 font-bold">{testimonial.growth}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* FAQ Section */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-white mb-4">
                  Frequently Asked Questions
                </h2>
              </div>

              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                  <h3 className="text-xl font-bold text-white mb-3">How can I grow my YouTube channel?</h3>
                  <p className="text-gray-300">Growing your YouTube channel requires understanding your analytics, optimizing your content strategy, and consistently creating engaging videos. YTuber provides detailed insights to help you identify what works and what doesn&apos;t.</p>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                  <h3 className="text-xl font-bold text-white mb-3">What YouTube growth strategies work best?</h3>
                  <p className="text-gray-300">The most effective YouTube growth strategies include optimizing thumbnails, improving video titles, understanding your audience demographics, posting consistently, and analyzing your competition. Our platform provides actionable insights for all these areas.</p>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                  <h3 className="text-xl font-bold text-white mb-3">How long does it take to see YouTube growth?</h3>
                  <p className="text-gray-300">YouTube growth timelines vary, but with proper analytics and strategy implementation, most creators see improvements within 30-90 days. Consistent application of data-driven insights accelerates growth significantly.</p>
                </div>
              </div>
            </div>

            {/* CTA Section */}
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
                <h2 className="text-4xl font-bold text-white mb-6">
                  Start Growing Your YouTube Channel Today
                </h2>
                <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                  Get detailed analytics, growth insights, and actionable recommendations
                  to take your YouTube channel to the next level.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Get Your Growth Report
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                  <Link
                    href="/blog/how-to-grow-youtube-channel-2024"
                    className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                  >
                    Read Growth Guide
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
