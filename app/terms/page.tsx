import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Terms of Sale - YTuber',
  description: 'YTuber terms of sale and service agreement.',
};

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">Terms of Sale</h1>
          
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <p className="text-gray-300 mb-6">
              <strong>Last updated:</strong> {new Date().toLocaleDateString()}
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Service Description</h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              YTuber provides advanced YouTube channel analytics and growth insights. Our services include:
            </p>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Comprehensive channel analysis reports</li>
              <li>• Advanced insights and recommendations</li>
              <li>• Memory system for progress tracking (pack users)</li>
              <li>• PDF and JSON report exports</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Pricing and Payment</h2>
            <h3 className="text-xl font-semibold text-white mb-3">Current Pricing:</h3>
            <ul className="text-gray-300 space-y-2 mb-4">
              <li>• First Timer: $2.00 (one-time discount)</li>
              <li>• Single Report: $4.99</li>
              <li>• Creator Pack: $19.99 (5 reports + memory system)</li>
              <li>• Pro Pack: $34.99 (10 reports + advanced features)</li>
            </ul>
            
            <p className="text-gray-300 leading-relaxed mb-6">
              All payments are processed securely through Iyzico. Prices are subject to change with notice.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Service Limitations</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Reports are generated based on available YouTube Analytics data</li>
              <li>• Analysis quality depends on channel data availability</li>
              <li>• Memory system requires multiple reports for comparison</li>
              <li>• Service availability subject to YouTube API limitations</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">User Responsibilities</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Provide accurate YouTube channel information</li>
              <li>• Maintain valid YouTube Analytics access</li>
              <li>• Use the service in compliance with YouTube&apos;s Terms of Service</li>
              <li>• Not share or resell generated reports without permission</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Intellectual Property</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              Generated reports and insights remain your property. However, the underlying technology,
              algorithms, and service methodology are proprietary to YTuber and protected by intellectual property laws.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Service Availability</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              We strive for 99.9% uptime but cannot guarantee uninterrupted service. Scheduled maintenance 
              will be announced in advance. Service credits may be provided for extended outages.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Limitation of Liability</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              YTuber provides insights and recommendations based on data analysis. We are not responsible 
              for business decisions made based on our reports. Our liability is limited to the amount paid for services.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Termination</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              Either party may terminate this agreement at any time. Upon termination, you retain access 
              to previously generated reports, but no new services will be provided.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Changes to Terms</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              We may update these terms periodically. Continued use of the service constitutes acceptance 
              of updated terms. Material changes will be communicated via email.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Contact Information</h2>
            <p className="text-gray-300 leading-relaxed">
              For questions about these terms or our services, please contact us. 
              We are committed to providing excellent customer service and support.
            </p>
          </div>
          
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
