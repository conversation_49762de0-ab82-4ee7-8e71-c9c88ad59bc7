"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/app/providers/AuthContext";

export default function TopNav() {
  const { user } = useAuth();
  const pathname = usePathname();

  const link = (href: string, label: string) => (
    <Link
      href={href}
      className={`px-3 py-2 rounded-md text-sm font-medium ${
        pathname === href ? "bg-white/10 text-white" : "text-gray-300 hover:text-white"
      }`}
    >
      {label}
    </Link>
  );

  return (
    <nav className="sticky top-0 z-50 backdrop-blur bg-black/30 border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 py-3 flex items-center gap-4">
        <Link href="/" className="text-white font-bold text-lg">ytuber</Link>
        <div className="flex-1" />
        {link("/", "Home")}
        {link("/pricing", "Pricing")}
        {user ? (
          <>
            {link("/dashboard", "Dashboard")}
            {link("/profile", "Profile")}
          </>
        ) : (
          link("/auth/signin", "Sign in")
        )}
      </div>
    </nav>
  );
}

