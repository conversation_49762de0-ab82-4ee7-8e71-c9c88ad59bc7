"use client";

import { useAuth } from "@/app/providers/AuthContext";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";

interface Purchase {
  id: string;
  uid: string;
  type: string; // pack type
  iyzicoPaymentId?: string | null;
  status: "paid" | "failed" | string;
  createdAt: string | Date;
}

export default function InvoicesPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [purchases, setPurchases] = useState<Purchase[]>([]);

  useEffect(() => {
    if (!user) return;
    (async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch("/api/user/purchases", { headers: { Authorization: `Bearer ${token}` } });
        if (res.ok) {
          const data = await res.json();
          setPurchases(data.purchases || []);
        }
      } catch (e) {
        // ignore
      } finally {
        setLoading(false);
      }
    })();
  }, [user]);

  const statusBadge = (status: string) => {
    const cls = status === "paid"
      ? "text-green-300 bg-green-500/10 border-green-400/30"
      : status === "failed"
      ? "text-red-300 bg-red-500/10 border-red-400/30"
      : "text-gray-300 bg-white/10 border-white/20";
    const label = status === "paid" ? "Successful" : status === "failed" ? "Failed" : status;
    return <span className={`px-2 py-1 rounded-full text-xs border ${cls}`}>{label}</span>;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="max-w-4xl mx-auto px-6 py-10">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-bold text-white">Invoices</h1>
          <Link href="/dashboard" className="text-white/80 hover:text-white underline">Back to Dashboard</Link>
        </div>

        {loading ? (
          <div className="text-white/80">Loading…</div>
        ) : purchases.length === 0 ? (
          <div className="text-white/80">No payments found.</div>
        ) : (
          <div className="space-y-3">
            {purchases.map((p, i) => (
              <motion.div
                key={p.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: i * 0.05 }}
                className="bg-white/10 border border-white/20 rounded-xl p-4 flex items-center justify-between"
              >
                <div>
                  <div className="text-white font-semibold">{p.type?.toUpperCase?.() || "Unknown"}</div>
                  <div className="text-sm text-gray-300">{new Date(p.createdAt).toLocaleString()}</div>
                  {p.iyzicoPaymentId && (
                    <div className="text-xs text-gray-400">Payment ID: {p.iyzicoPaymentId}</div>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  {statusBadge(p.status)}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

