"use client";

import { useAuth } from "../providers/AuthContext";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import {
  Calendar,
  Clock,
  Eye,
  FileText,
  Plus,
  Search,
  TrendingUp,
  Youtube,
  Download,
  ExternalLink
} from "lucide-react";
import Link from "next/link";

import { useSearchParams, useRouter } from "next/navigation";

function PaymentToast() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const status = searchParams?.get("payment");

  if (!status) return null;

  const isSuccess = status === "success";
  const bg = isSuccess ? "bg-green-500/15 border-green-400/30 text-green-200" : "bg-red-500/15 border-red-400/30 text-red-200";
  const title = isSuccess ? "Ödemeniz başarıyla tamamlandı" : "Ödeme başarısız oldu";
  const desc = isSuccess ? "Kredileriniz hesabınıza tanımlandı." : "Tekrar deneyebilir veya farklı bir yöntem seçebilirsiniz.";

  return (
    <div className="fixed top-20 left-1/2 -translate-x-1/2 z-50">
      <div className={`border ${bg} px-4 py-3 rounded-xl shadow-lg backdrop-blur-sm flex items-center gap-3`}
           role="status">
        <span className="font-semibold">{title}</span>
        <span className="text-sm opacity-80">{desc}</span>
        <button
          onClick={() => router.push("/dashboard")}
          className="ml-2 text-white/80 hover:text-white underline text-sm"
        >Kapat</button>
      </div>
    </div>
  );
}

interface Report {
  id: string;
  channelName: string;
  channelUrl: string;
  status: "pending" | "completed" | "failed";
  createdAt: string;
  data?: {
    subscribers: number;
    totalViews: number;
    avgViews: number;
    engagement: number;
  };
}

export default function Dashboard() {
  const { user, loading: authLoading } = useAuth();
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [userPackInfo, setUserPackInfo] = useState<any>(null);

  // Fetch real user data and reports
  useEffect(() => {
    if (user) {
      fetchUserData();
    }
  }, [user]);

  const fetchUserData = async () => {
    try {
      // Get user's reports from Firebase
      const token = await user?.getIdToken();
      if (!token) return;

      const [reportsRes, packInfoRes] = await Promise.all([
        fetch('/api/user/reports', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/user/pack-info', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      if (reportsRes.ok) {
        const { reports } = await reportsRes.json();
        setReports(reports || []);
      }

      if (packInfoRes.ok) {
        const packInfo = await packInfoRes.json();
        setUserPackInfo(packInfo);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Fallback to empty data
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  // No mock data. Show real reports only; empty state will handle zero items.
  const displayReports = reports;

  const filteredReports = displayReports.filter(report => {
    const matchesSearch = report.channelName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || report.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Decide where New Analysis should go based on credits
  const newAnalysisHref = (userPackInfo?.packCredits ?? 0) > 0 ? "/auth/select-channel" : "/checkout";


  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-green-400 bg-green-400/10 border-green-400/20";
      case "pending": return "text-yellow-400 bg-yellow-400/10 border-yellow-400/20";
      case "failed": return "text-red-400 bg-red-400/10 border-red-400/20";
      default: return "text-gray-400 bg-gray-400/10 border-gray-400/20";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return <FileText className="w-4 h-4" />;
      case "pending": return <Clock className="w-4 h-4" />;
      case "failed": return <ExternalLink className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full"
        />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-300">Please sign in to access your dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <motion.header
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="border-b border-white/10 bg-black/20 backdrop-blur-sm"
        >
          <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Youtube className="w-5 h-5 text-white" />
                </div>
                <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  ytuber
                </span>
              </Link>
              <div className="hidden sm:block w-px h-6 bg-white/20 mx-2" />
              <h1 className="hidden sm:block text-xl font-semibold text-white">Dashboard</h1>
            </div>

            <div className="flex items-center gap-4">
              {/* Credits Display */}
              {userPackInfo && (
                <div className="hidden sm:flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-3 py-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm text-white font-medium">
                    {userPackInfo.packCredits} Credits
                  </span>
                </div>
              )}

              <Image
                src={user.photoURL || '/default-avatar.svg'}
                alt={user.displayName || 'User'}
                width={32}
                height={32}
                className="w-8 h-8 rounded-full border-2 border-white/20"
              />
              <span className="hidden sm:block text-white font-medium">{user.displayName}</span>
            </div>
          </div>
        </motion.header>

        {/* Payment toasts */}
        <PaymentToast />

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Stats Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            {[
              { label: "Total Reports", value: displayReports.length, icon: FileText, color: "from-blue-400 to-purple-500" },
              { label: "Completed", value: displayReports.filter(r => r.status === "completed").length, icon: TrendingUp, color: "from-green-400 to-blue-500" },
              { label: "Credits Left", value: userPackInfo?.packCredits || 0, icon: Clock, color: "from-yellow-400 to-orange-500" },
              { label: "This Month", value: displayReports.filter(r => new Date(r.createdAt).getMonth() === new Date().getMonth()).length, icon: Calendar, color: "from-pink-400 to-red-500" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 + index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">{stat.value}</div>
                    <div className="text-sm text-gray-300">{stat.label}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Controls */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 mb-8"
          >
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl pl-12 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-pink-400 transition-colors"
              />
            </div>

            <div className="flex gap-3">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-pink-400 transition-colors"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>

              <Link href="/dashboard/invoices" className="hidden sm:block">
                <motion.button
                  className="px-6 py-3 bg-white/10 text-white rounded-xl font-semibold border border-white/20 hover:bg-white/20 transition-all flex items-center gap-2"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Invoices
                </motion.button>
              </Link>

              <Link href={newAnalysisHref}>
                <motion.button
                  className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-semibold shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/40 transition-all flex items-center gap-2"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Plus className="w-5 h-5" />
                  New Analysis
                </motion.button>
              </Link>
            </div>
          </motion.div>

          {/* Reports List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 animate-pulse">
                    <div className="h-4 bg-white/20 rounded mb-3"></div>
                    <div className="h-3 bg-white/10 rounded mb-2"></div>
                    <div className="h-3 bg-white/10 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : filteredReports.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FileText className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No reports found</h3>
                <p className="text-gray-300 mb-6">
                  {searchTerm || filterStatus !== "all"
                    ? "Try adjusting your search or filter criteria"
                    : "Start by analyzing your first YouTube channel"}
                </p>
                <Link href={newAnalysisHref}>
                  <motion.button
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-semibold shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/40 transition-all"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Create Your First Report
                  </motion.button>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredReports.map((report, index) => (
                  <motion.div
                    key={report.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all group cursor-pointer"
                    whileHover={{ scale: 1.02, y: -5 }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
                          <Youtube className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white group-hover:text-pink-300 transition-colors">
                            {report.channelName}
                          </h3>
                          <p className="text-sm text-gray-400">
                            {new Date(report.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className={`px-3 py-1 rounded-full border text-xs font-medium flex items-center gap-1 ${getStatusColor(report.status)}`}>
                        {getStatusIcon(report.status)}
                        {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                      </div>
                    </div>

                    {report.data && (
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">{(report.data.subscribers / 1000).toFixed(1)}K</div>
                          <div className="text-xs text-gray-400">Subscribers</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">{(report.data.avgViews / 1000).toFixed(1)}K</div>
                          <div className="text-xs text-gray-400">Avg Views</div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <Link href={`/dashboard/reports/${report.id}`} className="flex-1">
                        <motion.button
                          className="w-full py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <Eye className="w-4 h-4" />
                          View Report
                        </motion.button>
                      </Link>
                      {report.status === "completed" && (
                        <motion.button
                          className="ml-2 p-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Download className="w-4 h-4" />
                        </motion.button>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
