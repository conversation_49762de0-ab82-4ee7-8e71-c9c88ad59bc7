"use client";

import { useAuth } from "../../../providers/AuthContext";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  BarChart3,
  Calendar,
  Download,
  Eye,
  Heart,
  Play,
  Target,
  TrendingUp,
  Users,
  Youtube,
  Zap,
  Lightbulb,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

interface ReportData {
  id: string;
  channelName: string;
  channelUrl: string;
  status: "completed";
  createdAt: string;
  data: {
    overview: {
      subscribers: number;
      totalViews: number;
      totalVideos: number;
      avgViews: number;
      engagement: number;
      uploadFrequency: string;
      channelAge: number;
      topPerformingVideo: {
        title: string;
        views: number;
        thumbnail: string;
      };
    };
    algorithmAnalysis: {
      ctrOptimization: {
        currentCTR: string;
        improvementPotential: string;
        strategies: string[];
        titleOptimization: string[];
      };
      watchTimeAnalysis: {
        averageWatchTime: string;
        retentionIssues: string[];
        improvementStrategies: string[];
      };
      algorithmCompatibility: {
        currentScore: number;
        strengths: string[];
        weaknesses: string[];
        optimizationSteps: string[];
      };
    };
    swotAnalysis: {
      strengths: string[];
      weaknesses: string[];
      opportunities: string[];
      threats: string[];
    };
    contentStrategy: {
      videoIdeas: {
        title: string;
        hook: string;
        description: string;
        estimatedViews: number;
        difficulty: string;
        trendingPotential: string;
        keywords: string[];
      }[];
      contentPillars: string[];
      trendingTopics: string[];
    };
    thumbnailAnalysis: {
      currentPerformance: {
        averageCTR: string;
        topPerformingElements: string[];
        underperformingElements: string[];
      };
      designPrinciples: {
        effective: string[];
        improvements: string[];
      };
      colorAnalysis: {
        bestPerforming: string[];
        recommendations: string[];
      };
      improvementSuggestions: string[];
    };
    growthPlan: {
      shortTerm: {
        weeks1to4: string[];
        expectedResults: string[];
      };
      mediumTerm: {
        months1to3: string[];
        expectedResults: string[];
      };
      longTerm: {
        months3to12: string[];
        expectedResults: string[];
      };
      milestones: {
        target: string;
        timeframe: string;
        metrics: string[];
      }[];
    };
    recommendations: {
      immediate: string[];
      priority: string[];
      strategic: string[];
      experimental: string[];
    };
    predictions: {
      viewsGrowth: string;
      subscriberGrowth: string;
      engagementImprovement: string;
      revenueProjection: string;
    };
  };
}

export default function ReportDetail() {
  const { user, loading: authLoading } = useAuth();
  const params = useParams();
  const [report, setReport] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  // Load real report data
  useEffect(() => {
    (async () => {
      if (!user || !params.id) return;
      try {
        const res = await fetch(`/api/reports/${params.id}`);
        if (!res.ok) throw new Error("Report not found");
        const { jsonUrl } = await res.json();
        const jsonRes = await fetch(jsonUrl);
        if (!jsonRes.ok) throw new Error("Failed to load report json");
        const reportData = await jsonRes.json();
        setReport(reportData);
      } catch (e) {
        console.error("Failed to load report:", e);
        setReport(null);
      } finally {
        setLoading(false);
      }
    })();
  }, [user, params.id]);

/*
  // existing render continues...

            // Algorithm Analysis
            algorithmAnalysis: {
              ctrOptimization: {
                currentCTR: "3.2%",
                improvementPotential: "45%",
                strategies: [
                  "Optimize thumbnail contrast and emotional appeal",
                  "Use power words in titles (Ultimate, Secret, Proven)",
                  "Test different thumbnail styles A/B",
                  "Include numbers and specific benefits in titles"
                ],
                titleOptimization: [
                  "Keep titles under 60 characters for mobile",
                  "Front-load the most important keywords",
                  "Use emotional triggers and urgency",
                  "Include target audience in title when relevant"
                ]
              },
              watchTimeAnalysis: {
                averageWatchTime: "45%",
                retentionIssues: [
                  "Slow introductions losing 23% of viewers in first 15 seconds",
                  "Lack of visual engagement in middle sections",
                  "Missing pattern interrupts every 30-60 seconds"
                ],
                improvementStrategies: [
                  "Start with compelling hook showing end result",
                  "Use jump cuts and visual aids every 10-15 seconds",
                  "Preview what's coming next at retention drop points",
                  "Add captions and graphics to maintain visual interest"
                ]
              },
              algorithmCompatibility: {
                currentScore: 7.2,
                strengths: [
                  "Consistent upload schedule builds algorithm trust",
                  "Good engagement rate signals quality content",
                  "Relevant content for established niche"
                ],
                weaknesses: [
                  "Low click-through rate hurts discoverability",
                  "Inconsistent video length confuses algorithm",
                  "Limited cross-video engagement (playlists, end screens)"
                ],
                optimizationSteps: [
                  "Improve CTR through better thumbnails and titles",
                  "Create playlists to increase session duration",
                  "Use end screens and cards to promote related videos",
                  "Optimize for suggested videos with similar content"
                ]
              }
            },
            // SWOT Analysis
            swotAnalysis: {
              strengths: [
                "Consistent upload schedule builds audience trust and algorithm favor",
                "High engagement rate (4.2%) indicates strong community connection",
                "Technical expertise creates authority in niche",
                "Professional production quality sets apart from competitors",
                "Loyal subscriber base with high retention rate"
              ],
              weaknesses: [
                "Click-through rate below industry average (3.2% vs 4-5%)",
                "Inconsistent video length confuses algorithm optimization",
                "Limited use of trending topics and viral content strategies",
                "Underutilized community features (polls, community posts)",
                "Slow adaptation to new platform features (Shorts, Live)"
              ],
              opportunities: [
                "Expand into related tech categories (smartphones, gaming, AI)",
                "Collaborate with other tech reviewers for cross-promotion",
                "Create educational series to increase binge-watching",
                "Leverage YouTube Shorts for algorithm boost and new audience",
                "Develop premium content or courses for monetization",
                "Partner with tech brands for sponsored content"
              ],
              threats: [
                "Increasing competition in tech review space",
                "Algorithm changes potentially affecting reach",
                "Seasonal fluctuations in tech product releases",
                "Platform dependency risk and potential policy changes",
                "Economic downturn affecting tech spending and ad revenue"
              ]
            },

            // Content Strategy
            contentStrategy: {
              videoIdeas: [
                {
                  title: "iPhone 15 vs Samsung Galaxy S24: The ULTIMATE Comparison",
                  hook: "Which flagship phone ACTUALLY wins in 2024?",
                  description: "Comprehensive comparison covering camera, performance, battery, and value for different user types",
                  estimatedViews: 45000,
                  difficulty: "medium",
                  trendingPotential: "high",
                  keywords: ["iPhone 15", "Galaxy S24", "phone comparison", "2024 flagship"]
                },
                {
                  title: "5 Tech Products That Will Change Your Life in 2024",
                  hook: "These gadgets will revolutionize your daily routine",
                  description: "Showcase innovative products with real-world testing and demonstrations",
                  estimatedViews: 32000,
                  difficulty: "easy",
                  trendingPotential: "high",
                  keywords: ["tech 2024", "best gadgets", "life changing tech", "innovation"]
                },
                {
                  title: "Building the PERFECT Tech Setup for Under $1000",
                  hook: "You won't believe what's possible with this budget",
                  description: "Budget-friendly tech setup guide with performance benchmarks and alternatives",
                  estimatedViews: 28000,
                  difficulty: "medium",
                  trendingPotential: "medium",
                  keywords: ["budget tech", "tech setup", "under 1000", "best value"]
                }
              ],
              contentPillars: [
                "Product Reviews & Comparisons",
                "Tech News & Industry Analysis",
                "Setup Guides & Tutorials",
                "Budget Tech Recommendations",
                "Future Tech Predictions"
              ],
              trendingTopics: [
                "AI integration in consumer devices",
                "Sustainable tech and eco-friendly gadgets",
                "Remote work technology solutions",
                "Gaming hardware evolution",
                "Privacy and security in tech"
              ]
            },
            // Thumbnail Analysis
            thumbnailAnalysis: {
              currentPerformance: {
                averageCTR: "3.2%",
                topPerformingElements: [
                  "High contrast colors (red/blue combinations)",
                  "Clear facial expressions showing emotion",
                  "Bold, readable text overlays",
                  "Consistent branding elements"
                ],
                underperformingElements: [
                  "Cluttered compositions with too many elements",
                  "Low contrast text that's hard to read on mobile",
                  "Generic product shots without context",
                  "Inconsistent color schemes across videos"
                ]
              },
              designPrinciples: {
                effective: [
                  "Rule of thirds composition creates visual balance",
                  "High contrast between subject and background",
                  "Consistent brand colors and typography",
                  "Clear focal point draws viewer attention"
                ],
                improvements: [
                  "Increase text size for mobile readability",
                  "Use more dynamic poses and expressions",
                  "Add visual elements like arrows or highlights",
                  "Create templates for different video types"
                ]
              },
              colorAnalysis: {
                bestPerforming: ["#FF6B6B (Red)", "#4ECDC4 (Teal)", "#45B7D1 (Blue)"],
                recommendations: [
                  "Use warm colors for emotional content",
                  "Cool colors for technical/professional content",
                  "High contrast combinations for better visibility",
                  "Consistent brand color palette across all thumbnails"
                ]
              },
              improvementSuggestions: [
                "A/B test thumbnails with and without text overlays",
                "Create thumbnail templates for different content types",
                "Use consistent branding elements (logo, colors, fonts)",
                "Test different emotional expressions and poses",
                "Optimize for mobile viewing (larger text, simpler compositions)"
              ]
            },

            // Growth Plan
            growthPlan: {
              shortTerm: {
                weeks1to4: [
                  "Optimize top 10 video titles and thumbnails for better CTR",
                  "Create 3 high-potential videos from trending topics list",
                  "Implement better end screens and cards in existing videos",
                  "Start weekly community posts to increase engagement",
                  "Set up email list capture for direct audience communication"
                ],
                expectedResults: [
                  "20-30% increase in click-through rate",
                  "15% boost in overall engagement",
                  "500-1000 new email subscribers"
                ]
              },
              mediumTerm: {
                months1to3: [
                  "Launch 'Tech Explained Simply' series for broader appeal",
                  "Collaborate with 3-5 other tech creators for cross-promotion",
                  "Optimize entire channel for search discovery",
                  "Create comprehensive playlists for better session duration",
                  "Experiment with YouTube Shorts format"
                ],
                expectedResults: [
                  "50% increase in subscriber growth rate",
                  "40% boost in average monthly views",
                  "Improved search ranking for target keywords"
                ]
              },
              longTerm: {
                months3to12: [
                  "Expand into adjacent niches (gaming, productivity, lifestyle tech)",
                  "Develop premium content offerings (courses, exclusive reviews)",
                  "Build strategic partnerships with tech brands",
                  "Create evergreen content library for consistent traffic",
                  "Establish thought leadership through industry commentary"
                ],
                expectedResults: [
                  "100% subscriber growth (250K total)",
                  "Multiple revenue streams beyond AdSense",
                  "Recognition as authority in tech space"
                ]
              },
              milestones: [
                {
                  target: "150K subscribers",
                  timeframe: "3 months",
                  metrics: ["Monthly subscriber growth", "Video performance consistency"]
                },
                {
                  target: "250K subscribers",
                  timeframe: "6 months",
                  metrics: ["Average views per video", "Engagement rate improvement"]
                },
                {
                  target: "1M monthly views",
                  timeframe: "9 months",
                  metrics: ["Search traffic growth", "Suggested video performance"]
                }
              ]
            },

            // Performance Predictions
            predictions: {
              viewsGrowth: "35-50% monthly increase with optimization",
              subscriberGrowth: "40-60% monthly increase with consistent content",
              engagementImprovement: "25-40% increase with community focus",
              revenueProjection: "$2,000-5,000 monthly within 6 months"
            },
            // Immediate Action Items
            recommendations: {
              immediate: [
                "Update top 5 video thumbnails with high-contrast designs",
                "Optimize channel banner and about section for search",
                "Create end screens for top 10 performing videos",
                "Set up community tab posting schedule"
              ],
              priority: [
                "Launch 'Tech Explained Simply' series for broader appeal",
                "Implement comprehensive SEO optimization across all videos",
                "Create collaboration outreach list for cross-promotion",
                "Develop content calendar for next 3 months"
              ],
              strategic: [
                "Build email list for direct audience communication",
                "Develop multiple revenue streams beyond AdSense",
                "Create evergreen content library for consistent traffic",
                "Establish thought leadership through industry commentary"
              ],
              experimental: [],
      } catch (e) {
        console.error("Failed to load report:", e);
        setReport(null);
      } finally {
        setLoading(false);
      }
    })();
  }, [user, params.id]);

*/

  const tabs = [
    { id: "overview", label: "Overview", icon: BarChart3 },
    { id: "algorithm", label: "Algorithm", icon: TrendingUp },
    { id: "swot", label: "SWOT Analysis", icon: Target },
    { id: "content", label: "Content Strategy", icon: Play },
    { id: "thumbnails", label: "Thumbnails", icon: Eye },
    { id: "growth", label: "Growth Plan", icon: CheckCircle },
    { id: "predictions", label: "Predictions", icon: Lightbulb }
  ];



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <motion.header
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="border-b border-white/10 bg-black/20 backdrop-blur-sm"
        >
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link href="/dashboard">
                  <motion.button
                    className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
                    whileHover={{ x: -5 }}
                  >
                    <ArrowLeft className="w-5 h-5" />
                    Back to Dashboard
                  </motion.button>
                </Link>
                <div className="hidden sm:block w-px h-6 bg-white/20 mx-2" />
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <Youtube className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-white">{report.channelName}</h1>
                    <p className="text-sm text-gray-300">Report generated on {new Date(report.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>

              <motion.button
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Download className="w-4 h-4" />
                Download PDF
              </motion.button>
            </div>
          </div>
        </motion.header>

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Tab Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex flex-wrap gap-2 mb-8 bg-white/5 backdrop-blur-sm rounded-2xl p-2"
          >
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all ${
                  activeTab === tab.id
                    ? "bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-white/10"
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <tab.icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.label}</span>
              </motion.button>
            ))}
          </motion.div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              {activeTab === "overview" && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Key Metrics */}
                  <div className="lg:col-span-2">
                    <h2 className="text-2xl font-bold text-white mb-6">Channel Overview</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                      {[
                        { label: "Subscribers", value: report.data.overview.subscribers.toLocaleString(), icon: Users, color: "from-blue-400 to-purple-500" },
                        { label: "Total Views", value: (report.data.overview.totalViews / 1000000).toFixed(1) + "M", icon: Eye, color: "from-green-400 to-blue-500" },
                        { label: "Videos", value: report.data.overview.totalVideos.toString(), icon: Play, color: "from-yellow-400 to-orange-500" },
                        { label: "Avg Views", value: report.data.overview.avgViews.toLocaleString(), icon: TrendingUp, color: "from-pink-400 to-red-500" },
                        { label: "Engagement", value: report.data.overview.engagement.toFixed(1) + "%", icon: Heart, color: "from-purple-400 to-pink-500" },
                        { label: "Upload Freq", value: report.data.overview.uploadFrequency, icon: Calendar, color: "from-indigo-400 to-purple-500" }
                      ].map((metric, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                        >
                          <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-xl flex items-center justify-center mb-4`}>
                            <metric.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="text-2xl font-bold text-white mb-1">{metric.value}</div>
                          <div className="text-sm text-gray-300">{metric.label}</div>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Quick Insights */}
                  <div>
                    <h3 className="text-xl font-bold text-white mb-6">Quick Insights</h3>
                    <div className="space-y-4">
                      <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="w-5 h-5 text-green-400" />
                          <span className="font-semibold text-green-400">Strength</span>
                        </div>
                        <p className="text-sm text-gray-300">High engagement rate shows strong community connection</p>
                      </div>
                      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="w-5 h-5 text-yellow-400" />
                          <span className="font-semibold text-yellow-400">Opportunity</span>
                        </div>
                        <p className="text-sm text-gray-300">Video titles need optimization for better CTR</p>
                      </div>
                      <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Info className="w-5 h-5 text-blue-400" />
                          <span className="font-semibold text-blue-400">Recommendation</span>
                        </div>
                        <p className="text-sm text-gray-300">Focus on series content to increase watch time</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "algorithm" && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-white mb-6">Algorithm Analysis & Optimization</h2>

                  {/* CTR Optimization */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <Target className="w-6 h-6 text-blue-400" />
                      Click-Through Rate Optimization
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-4">
                          <div className="text-2xl font-bold text-blue-400">{report.data.algorithmAnalysis.ctrOptimization.currentCTR}</div>
                          <div className="text-sm text-gray-300">Current CTR</div>
                          <div className="text-green-400 text-sm mt-1">
                            +{report.data.algorithmAnalysis.ctrOptimization.improvementPotential} potential improvement
                          </div>
                        </div>
                        <h4 className="font-semibold text-white mb-2">Optimization Strategies:</h4>
                        <ul className="space-y-2">
                          {report.data.algorithmAnalysis.ctrOptimization.strategies.map((strategy, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                              {strategy}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white mb-2">Title Optimization:</h4>
                        <ul className="space-y-2">
                          {report.data.algorithmAnalysis.ctrOptimization.titleOptimization.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Watch Time Analysis */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <Play className="w-6 h-6 text-purple-400" />
                      Watch Time Analysis
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4 mb-4">
                          <div className="text-2xl font-bold text-purple-400">{report.data.algorithmAnalysis.watchTimeAnalysis.averageWatchTime}</div>
                          <div className="text-sm text-gray-300">Average Watch Time</div>
                        </div>
                        <h4 className="font-semibold text-white mb-2">Retention Issues:</h4>
                        <ul className="space-y-2">
                          {report.data.algorithmAnalysis.watchTimeAnalysis.retentionIssues.map((issue, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <AlertTriangle className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                              {issue}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-white mb-2">Improvement Strategies:</h4>
                        <ul className="space-y-2">
                          {report.data.algorithmAnalysis.watchTimeAnalysis.improvementStrategies.map((strategy, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                              {strategy}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Algorithm Compatibility */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <TrendingUp className="w-6 h-6 text-green-400" />
                      Algorithm Compatibility Score
                    </h3>
                    <div className="grid md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-green-400 mb-2">{report.data.algorithmAnalysis.algorithmCompatibility.currentScore}/10</div>
                        <div className="text-sm text-gray-300">Current Score</div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-400 mb-2">Strengths:</h4>
                        <ul className="space-y-1">
                          {report.data.algorithmAnalysis.algorithmCompatibility.strengths.map((strength, index) => (
                            <li key={index} className="text-sm text-gray-300 flex items-start gap-2">
                              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                              {strength}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-red-400 mb-2">Weaknesses:</h4>
                        <ul className="space-y-1">
                          {report.data.algorithmAnalysis.algorithmCompatibility.weaknesses.map((weakness, index) => (
                            <li key={index} className="text-sm text-gray-300 flex items-start gap-2">
                              <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                              {weakness}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "swot" && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-white mb-6">SWOT Analysis</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {[
                      { title: "Strengths", items: report.data.swotAnalysis.strengths, color: "green", icon: CheckCircle },
                      { title: "Weaknesses", items: report.data.swotAnalysis.weaknesses, color: "red", icon: AlertTriangle },
                      { title: "Opportunities", items: report.data.swotAnalysis.opportunities, color: "blue", icon: TrendingUp },
                      { title: "Threats", items: report.data.swotAnalysis.threats, color: "yellow", icon: AlertTriangle }
                    ].map((section, index) => (
                    <motion.div
                      key={section.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className={`bg-${section.color}-500/10 border border-${section.color}-500/20 rounded-2xl p-6`}
                    >
                      <div className="flex items-center gap-3 mb-6">
                        <section.icon className={`w-6 h-6 text-${section.color}-400`} />
                        <h3 className={`text-xl font-bold text-${section.color}-400`}>{section.title}</h3>
                      </div>
                      <ul className="space-y-3">
                        {section.items.map((item, itemIndex) => (
                          <motion.li
                            key={itemIndex}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: itemIndex * 0.1 }}
                            className="flex items-start gap-3 text-gray-300"
                          >
                            <div className={`w-2 h-2 bg-${section.color}-400 rounded-full mt-2 flex-shrink-0`} />
                            <span className="text-sm leading-relaxed">{item}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </motion.div>
                  ))}
                  </div>
                </div>
              )}

              {activeTab === "content" && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-white mb-6">Content Strategy & Video Ideas</h2>

                  {/* Video Ideas */}
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
                      <Play className="w-6 h-6 text-red-400" />
                      High-Potential Video Ideas
                    </h3>
                    <div className="grid gap-6">
                      {report.data.contentStrategy.videoIdeas.map((idea, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                        >
                          <div className="flex items-start gap-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                              <Play className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <h4 className="text-lg font-semibold text-white mb-2">{idea.title}</h4>
                              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 mb-3">
                                <div className="flex items-center gap-2 mb-1">
                                  <Zap className="w-4 h-4 text-yellow-400" />
                                  <span className="text-sm font-medium text-yellow-400">Hook</span>
                                </div>
                                <p className="text-sm text-gray-300">{idea.hook}</p>
                              </div>
                              <p className="text-gray-300 mb-4">{idea.description}</p>
                              <div className="flex items-center gap-4 flex-wrap">
                                <div className="flex items-center gap-2">
                                  <Eye className="w-4 h-4 text-blue-400" />
                                  <span className="text-sm text-blue-400">Est. {idea.estimatedViews.toLocaleString()} views</span>
                                </div>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  idea.difficulty === 'easy' ? 'bg-green-500/20 text-green-400' :
                                  idea.difficulty === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                                  'bg-red-500/20 text-red-400'
                                }`}>
                                  {idea.difficulty.toUpperCase()}
                                </span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  idea.trendingPotential === 'high' ? 'bg-red-500/20 text-red-400' :
                                  idea.trendingPotential === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                                  'bg-gray-500/20 text-gray-400'
                                }`}>
                                  {idea.trendingPotential.toUpperCase()} TREND
                                </span>
                              </div>
                              <div className="mt-3">
                                <div className="text-xs text-gray-400 mb-1">Keywords:</div>
                                <div className="flex flex-wrap gap-1">
                                  {idea.keywords.map((keyword, kIndex) => (
                                    <span key={kIndex} className="px-2 py-1 bg-white/5 rounded text-xs text-gray-300">
                                      {keyword}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Content Pillars */}
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                        <Target className="w-6 h-6 text-purple-400" />
                        Content Pillars
                      </h3>
                      <div className="space-y-3">
                        {report.data.contentStrategy.contentPillars.map((pillar, index) => (
                          <div key={index} className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                            <div className="text-white font-medium">{pillar}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                        <TrendingUp className="w-6 h-6 text-green-400" />
                        Trending Topics
                      </h3>
                      <div className="space-y-3">
                        {report.data.contentStrategy.trendingTopics.map((topic, index) => (
                          <div key={index} className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                            <div className="text-white font-medium">{topic}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}





              {activeTab === "thumbnails" && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-white mb-6">Thumbnail Analysis & Optimization</h2>

                  {/* Current Performance */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <Eye className="w-6 h-6 text-blue-400" />
                      Current Performance Analysis
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-4">
                          <div className="text-2xl font-bold text-blue-400">{report.data.thumbnailAnalysis.currentPerformance.averageCTR}</div>
                          <div className="text-sm text-gray-300">Average CTR</div>
                        </div>
                        <h4 className="font-semibold text-green-400 mb-2">Top Performing Elements:</h4>
                        <ul className="space-y-2">
                          {report.data.thumbnailAnalysis.currentPerformance.topPerformingElements.map((element, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                              {element}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-red-400 mb-2">Underperforming Elements:</h4>
                        <ul className="space-y-2">
                          {report.data.thumbnailAnalysis.currentPerformance.underperformingElements.map((element, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm text-gray-300">
                              <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
                              {element}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Design Principles */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <Target className="w-6 h-6 text-purple-400" />
                      Design Principles Analysis
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-green-400 mb-3">Effective Principles:</h4>
                        <div className="space-y-3">
                          {report.data.thumbnailAnalysis.designPrinciples.effective.map((principle, index) => (
                            <div key={index} className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                              <div className="text-sm text-gray-300">{principle}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-yellow-400 mb-3">Areas for Improvement:</h4>
                        <div className="space-y-3">
                          {report.data.thumbnailAnalysis.designPrinciples.improvements.map((improvement, index) => (
                            <div key={index} className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                              <div className="text-sm text-gray-300">{improvement}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Color Analysis & Recommendations */}
                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                      <h3 className="text-lg font-semibold text-white mb-4">Best Performing Colors</h3>
                      <div className="space-y-3">
                        {report.data.thumbnailAnalysis.colorAnalysis.bestPerforming.map((color, index) => (
                          <div key={index} className="flex items-center gap-3">
                            <div className="w-6 h-6 rounded-full" style={{ backgroundColor: color.split(' ')[0] }}></div>
                            <span className="text-gray-300 text-sm">{color}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                      <h3 className="text-lg font-semibold text-white mb-4">Color Recommendations</h3>
                      <div className="space-y-2">
                        {report.data.thumbnailAnalysis.colorAnalysis.recommendations.map((rec, index) => (
                          <div key={index} className="text-sm text-gray-300 flex items-start gap-2">
                            <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                            {rec}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Improvement Suggestions */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
                      <Lightbulb className="w-6 h-6 text-yellow-400" />
                      Actionable Improvement Suggestions
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      {report.data.thumbnailAnalysis.improvementSuggestions.map((suggestion, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                        >
                          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4">
                            <Lightbulb className="w-6 h-6 text-white" />
                          </div>
                          <p className="text-sm text-gray-300">{suggestion}</p>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "growth" && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-white mb-6">Strategic Growth Plan</h2>

                  {/* Growth Timeline */}
                  <div className="grid gap-8">
                    {[
                      {
                        title: "Short-term (1-4 weeks)",
                        data: report.data.growthPlan.shortTerm,
                        color: "from-green-500 to-blue-500",
                        icon: "🚀"
                      },
                      {
                        title: "Medium-term (1-3 months)",
                        data: report.data.growthPlan.mediumTerm,
                        color: "from-blue-500 to-purple-500",
                        icon: "📈"
                      },
                      {
                        title: "Long-term (3-12 months)",
                        data: report.data.growthPlan.longTerm,
                        color: "from-purple-500 to-pink-500",
                        icon: "🎯"
                      }
                    ].map((phase, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                      >
                        <div className="flex items-center gap-3 mb-6">
                          <div className={`w-12 h-12 bg-gradient-to-r ${phase.color} rounded-xl flex items-center justify-center text-2xl`}>
                            {phase.icon}
                          </div>
                          <h3 className="text-xl font-semibold text-white">{phase.title}</h3>
                        </div>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-400" />
                              Action Items
                            </h4>
                            <ul className="space-y-2">
                              {('weeks1to4' in phase.data ? phase.data.weeks1to4 :
                                'months1to3' in phase.data ? phase.data.months1to3 :
                                phase.data.months3to12).map((task, taskIndex) => (
                                <li key={taskIndex} className="flex items-start gap-3 text-gray-300">
                                  <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                    'weeks1to4' in phase.data ? 'bg-green-400' :
                                    'months1to3' in phase.data ? 'bg-blue-400' : 'bg-purple-400'
                                  }`} />
                                  <span className="text-sm">{task}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
                              <Target className="w-5 h-5 text-blue-400" />
                              Expected Results
                            </h4>
                            <ul className="space-y-2">
                              {phase.data.expectedResults.map((result, resultIndex) => (
                                <li key={resultIndex} className="flex items-start gap-3 text-gray-300">
                                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                                  <span className="text-sm">{result}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Milestones */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
                      <Target className="w-6 h-6 text-yellow-400" />
                      Key Milestones
                    </h3>
                    <div className="grid md:grid-cols-3 gap-6">
                      {report.data.growthPlan.milestones.map((milestone, index) => (
                        <div key={index} className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
                          <div className="text-lg font-bold text-yellow-400 mb-2">{milestone.target}</div>
                          <div className="text-sm text-gray-300 mb-3">Target: {milestone.timeframe}</div>
                          <div className="space-y-1">
                            {milestone.metrics.map((metric, mIndex) => (
                              <div key={mIndex} className="text-xs text-gray-400">{metric}</div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Categories */}
                  <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[
                      { title: "Immediate Actions", items: report.data.recommendations.immediate, color: "red" },
                      { title: "Priority Tasks", items: report.data.recommendations.priority, color: "yellow" },
                      { title: "Strategic Goals", items: report.data.recommendations.strategic, color: "blue" },
                      { title: "Experiments", items: report.data.recommendations.experimental, color: "purple" }
                    ].map((category, index) => (
                      <div key={index} className={`bg-${category.color}-500/10 border border-${category.color}-500/20 rounded-xl p-4`}>
                        <h4 className={`font-semibold text-${category.color}-400 mb-3`}>{category.title}</h4>
                        <ul className="space-y-2">
                          {category.items.map((item, itemIndex) => (
                            <li key={itemIndex} className="text-xs text-gray-300">{item}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === "predictions" && (
                <div className="space-y-8">
                  <h2 className="text-2xl font-bold text-white mb-6">Growth Predictions & Projections</h2>

                  {/* Key Predictions */}
                  <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[
                      {
                        label: "Views Growth",
                        value: report.data.predictions.viewsGrowth,
                        icon: Eye,
                        color: "from-blue-400 to-purple-500",
                        description: "Monthly view increase potential"
                      },
                      {
                        label: "Subscriber Growth",
                        value: report.data.predictions.subscriberGrowth,
                        icon: Users,
                        color: "from-green-400 to-blue-500",
                        description: "Monthly subscriber growth rate"
                      },
                      {
                        label: "Engagement Boost",
                        value: report.data.predictions.engagementImprovement,
                        icon: Heart,
                        color: "from-pink-400 to-red-500",
                        description: "Engagement rate improvement"
                      },
                      {
                        label: "Revenue Potential",
                        value: report.data.predictions.revenueProjection,
                        icon: TrendingUp,
                        color: "from-yellow-400 to-orange-500",
                        description: "Estimated monthly revenue"
                      }
                    ].map((prediction, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                      >
                        <div className={`w-12 h-12 bg-gradient-to-r ${prediction.color} rounded-xl flex items-center justify-center mb-4`}>
                          <prediction.icon className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-2xl font-bold text-white mb-1">{prediction.value}</div>
                        <div className="text-sm font-medium text-gray-300 mb-2">{prediction.label}</div>
                        <div className="text-xs text-gray-400">{prediction.description}</div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Growth Trajectory */}
                  <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-6">Projected Growth Trajectory</h3>
                    <div className="grid md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-400 mb-2">3 Months</div>
                        <div className="text-sm text-gray-300 mb-4">Short-term Impact</div>
                        <ul className="text-xs text-gray-400 space-y-1">
                          <li>• 20-30% CTR improvement</li>
                          <li>• 15% engagement boost</li>
                          <li>• 50% subscriber growth</li>
                        </ul>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-400 mb-2">6 Months</div>
                        <div className="text-sm text-gray-300 mb-4">Medium-term Results</div>
                        <ul className="text-xs text-gray-400 space-y-1">
                          <li>• 100% subscriber growth</li>
                          <li>• 40% average view increase</li>
                          <li>• Multiple revenue streams</li>
                        </ul>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-400 mb-2">12 Months</div>
                        <div className="text-sm text-gray-300 mb-4">Long-term Vision</div>
                        <ul className="text-xs text-gray-400 space-y-1">
                          <li>• Industry thought leader</li>
                          <li>• Diversified content portfolio</li>
                          <li>• Sustainable growth model</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
