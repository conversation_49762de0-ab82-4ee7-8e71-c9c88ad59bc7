"use client";

import { useAuth } from "../../providers/AuthContext";
import { signOut } from "@/lib/auth";
import { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import {
  ArrowLeft,
  Bell,
  Edit3,
  Save,
  Shield,
  Trash2,
  User
} from "lucide-react";
import Link from "next/link";

export default function ProfilePage() {
  const { user, loading: authLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [notifications, setNotifications] = useState({
    email: true,
    reports: true,
    marketing: false
  });

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full"
        />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-300">Please sign in to access your profile.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <motion.header
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="border-b border-white/10 bg-black/20 backdrop-blur-sm"
        >
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link href="/dashboard">
                  <motion.button
                    className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
                    whileHover={{ x: -5 }}
                  >
                    <ArrowLeft className="w-5 h-5" />
                    Back to Dashboard
                  </motion.button>
                </Link>
                <div className="hidden sm:block w-px h-6 bg-white/20 mx-2" />
                <h1 className="text-xl font-semibold text-white">Profile & Settings</h1>
              </div>
            </div>
          </div>
        </motion.header>

        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Profile Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center">
                <div className="relative inline-block mb-4">
                  <Image
                    src={user.photoURL || '/default-avatar.svg'}
                    alt={user.displayName || 'User'}
                    width={96}
                    height={96}
                    className="w-24 h-24 rounded-full border-4 border-white/20"
                  />
                  <motion.button
                    className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Edit3 className="w-4 h-4 text-white" />
                  </motion.button>
                </div>
                
                <h2 className="text-xl font-bold text-white mb-2">{user.displayName}</h2>
                <p className="text-gray-300 mb-4">{user.email}</p>
                
                <div className="space-y-3">
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="text-sm text-gray-400">Member since</div>
                    <div className="text-white font-medium">January 2024</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="text-sm text-gray-400">Reports created</div>
                    <div className="text-white font-medium">4 reports</div>
                  </div>
                </div>

                <motion.button
                  onClick={() => signOut()}
                  className="w-full mt-6 py-3 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-xl font-medium transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Sign Out
                </motion.button>
              </div>
            </motion.div>

            {/* Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="lg:col-span-2 space-y-6"
            >
              {/* Account Settings */}
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                <div className="flex items-center gap-3 mb-6">
                  <User className="w-6 h-6 text-blue-400" />
                  <h3 className="text-xl font-bold text-white">Account Settings</h3>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Display Name</label>
                    <input
                      type="text"
                      value={user.displayName || ''}
                      disabled={!isEditing}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-pink-400 disabled:opacity-50 transition-colors"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input
                      type="email"
                      value={user.email || ''}
                      disabled
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-pink-400 disabled:opacity-50 transition-colors"
                    />
                    <p className="text-xs text-gray-400 mt-1">Email cannot be changed</p>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    {isEditing ? (
                      <>
                        <motion.button
                          onClick={() => setIsEditing(false)}
                          className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl font-medium flex items-center gap-2"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Save className="w-4 h-4" />
                          Save Changes
                        </motion.button>
                        <motion.button
                          onClick={() => setIsEditing(false)}
                          className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-xl font-medium transition-colors"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Cancel
                        </motion.button>
                      </>
                    ) : (
                      <motion.button
                        onClick={() => setIsEditing(true)}
                        className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-xl font-medium flex items-center gap-2 transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Edit3 className="w-4 h-4" />
                        Edit Profile
                      </motion.button>
                    )}
                  </div>
                </div>
              </div>

              {/* Notification Settings */}
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6">
                <div className="flex items-center gap-3 mb-6">
                  <Bell className="w-6 h-6 text-yellow-400" />
                  <h3 className="text-xl font-bold text-white">Notifications</h3>
                </div>
                
                <div className="space-y-4">
                  {[
                    { key: 'email', label: 'Email notifications', description: 'Receive updates via email' },
                    { key: 'reports', label: 'Report completion', description: 'Get notified when reports are ready' },
                    { key: 'marketing', label: 'Marketing emails', description: 'Receive product updates and tips' }
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between py-3">
                      <div>
                        <div className="text-white font-medium">{setting.label}</div>
                        <div className="text-sm text-gray-400">{setting.description}</div>
                      </div>
                      <motion.button
                        onClick={() => setNotifications(prev => ({ ...prev, [setting.key]: !prev[setting.key as keyof typeof prev] }))}
                        className={`relative w-12 h-6 rounded-full transition-colors ${
                          notifications[setting.key as keyof typeof notifications] ? 'bg-green-500' : 'bg-gray-600'
                        }`}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="absolute top-1 w-4 h-4 bg-white rounded-full"
                          animate={{
                            x: notifications[setting.key as keyof typeof notifications] ? 24 : 4
                          }}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      </motion.button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Danger Zone */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-6">
                <div className="flex items-center gap-3 mb-6">
                  <Shield className="w-6 h-6 text-red-400" />
                  <h3 className="text-xl font-bold text-red-400">Danger Zone</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Delete Account</div>
                      <div className="text-sm text-gray-400">Permanently delete your account and all data</div>
                    </div>
                    <motion.button
                      className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-xl font-medium transition-colors flex items-center gap-2"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
