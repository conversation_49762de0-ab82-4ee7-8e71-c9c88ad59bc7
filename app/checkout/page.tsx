"use client";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - world-countries has no types in this project
import countriesData from "world-countries";

import { useEffect, useState } from "react";
import { useAuth } from "../providers/AuthContext";
import dynamic from "next/dynamic";
import { motion, AnimatePresence } from "framer-motion";

// Build a single global list of countries once
const COUNTRY_OPTIONS = (countriesData as any[])
  .map((c) => ({ code: c.cca2 as string, name: c.name.common as string }))
  .sort((a, b) => a.name.localeCompare(b.name));
import {
  CreditCard,
  Shield,
  CheckCircle,
  ArrowLeft,
  Sparkles,
  Youtube,
  Star,
  Zap
} from "lucide-react";

function CheckoutPage() {
  const { user } = useAuth();
  const [plan, setPlan] = useState("single");
  const [token, setToken] = useState<string | null>(null);
  const [checkoutHost, setCheckoutHost] = useState<string | null>(null);
  const [paymentPageUrl, setPaymentPageUrl] = useState<string | null>(null);
  const [checkoutHtml, setCheckoutHtml] = useState<string | null>(null);
  const [secureReachable, setSecureReachable] = useState<boolean | null>(null);
  const [forceCpp, setForceCpp] = useState(false);
  const [paymentCurrency, setPaymentCurrency] = useState<string | null>(null);
  const [usedFxRate, setUsedFxRate] = useState<number | null>(null);
  const [finalPrice, setFinalPrice] = useState<string | null>(null);
  const [billing, setBilling] = useState({
    fullName: "",
    email: "",
    phone: "",
    country: "US",
    city: "",
    postalCode: "",
    address1: "",
    address2: "",
    stateRegion: "",

    isBusiness: false,
    companyName: "",
    taxId: "",
    nationalId: "",
  });



	// On mount: detect force=cpp flag and test reachability of secure.iyzipay.com
	useEffect(() => {
	  const qs = new URLSearchParams(window.location.search);
	  setForceCpp(qs.get('force') === 'cpp');
	  let cancelled = false;
	  const test = async () => {
	    try {
	      const controller = new AbortController();
	      const id = setTimeout(() => controller.abort(), 4000);
	      await fetch('https://secure.iyzipay.com', { method: 'HEAD', mode: 'no-cors', signal: controller.signal }).catch(() => {});
	      clearTimeout(id);
	      if (!cancelled) setSecureReachable(true);
	    } catch {
	      if (!cancelled) setSecureReachable(false);
	    }
	  };
	  test();
	  return () => { cancelled = true; };
	}, []);


  const validateBilling = (): string | null => {
    if (!billing.fullName) return "Full name is required.";
    if (!billing.email || !/^\S+@\S+\.\S+$/.test(billing.email)) return "A valid email is required.";
    if (!billing.phone) return "Phone number is required.";
    if (!billing.address1 || !billing.city || !billing.postalCode || !billing.country) {
      return "Address, city, postal code and country are required.";
    }
    if (!billing.stateRegion) {
      return "State / Region is required.";
    }
    if (billing.country === "TR" && !billing.isBusiness) {
      if (!/^\d{11}$/.test(billing.nationalId)) return "National ID (TCKN) is required and must be 11 digits.";
    }
    return null;
  };

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [channelPreview, setChannelPreview] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [channelId, setChannelId] = useState<string>("");
  const [queryId, setQueryId] = useState<string>("");
  const [userInfo, setUserInfo] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const countryName = COUNTRY_OPTIONS.find((c) => c.code === billing.country)?.name || billing.country;
  const isBillingValid = validateBilling() === null;

  const hasValidCheckoutForm = !!(checkoutHtml && /<iframe|<form/i.test(checkoutHtml));
  const plans: Record<string, any> = {
    first: {
      name: "First Timer",
      price: "$2.00",
      originalPrice: "$4.99",
      features: [
        "✅ Perfect for trying out our service",
        "✅ AI-Powered Analytics Dashboard",
        "✅ 2025 Algorithm Insights",
        "✅ Viral Content Recommendations",
        "✅ Growth Strategy Roadmap",
        "✅ Smart Export & Reports"
      ],
      color: "from-blue-400 to-purple-500",
      badge: "FIRST TIME OFFER",
      description: "Perfect for trying out our service"
    },
    single: {
      name: "Single Report",
      price: "$4.99",
      features: [
        "✅ One comprehensive analysis",
        "✅ AI-Powered Analytics Dashboard",
        "✅ 2025 Algorithm Insights",
        "✅ Viral Content Recommendations",
        "✅ Growth Strategy Roadmap",
        "✅ Smart Export & Reports"
      ],
      color: "from-purple-400 to-pink-500",
      description: "One comprehensive analysis"
    },
    creator: {
      name: "Creator Pack",
      price: "$19.99",
      features: [
        "✅ 5 reports + memory system",
        "✅ Everything in Single Report",
        "🧠 Memory System*",
        "📈 Progress tracking over time",
        "🔥 Trend analysis across reports",
        "⚡ Priority Support"
      ],
      color: "from-pink-400 to-red-500",
      badge: "MEMORY",
      description: "5 reports + memory system"
    },
    pro: {
      name: "Pro Pack",
      price: "$34.99",
      features: [
        "✅ 10 reports + advanced features",
        "✅ Everything in Creator Pack",
        "🧠 Advanced Memory System",
        "📊 Multi-channel comparison",
        "🔮 Predictive growth modeling",
        "🏆 Competitive benchmarking",
        "💎 Premium Support"
      ],
      color: "from-green-400 to-blue-500",
      popular: true,
      badge: "MOST POPULAR",
      description: "10 reports + advanced features"
    }
  };

  const createForm = async () => {
    setLoading(true);
    const validationError = validateBilling();
    if (validationError) {
      setError(validationError);
      console.warn("[checkout] validationError", validationError, { billing, plan, channelId, queryId });
      setLoading(false);
      return;
    }
    setError(null);
    try {
      const idToken = await user?.getIdToken?.();
      setCheckoutHtml(null);
      const payload = { plan, channelId, queryId, billing };
      console.log("[checkout] creating iyzico form with payload", payload);
      const res = await fetch("/api/payments/iyzico/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(idToken ? { Authorization: `Bearer ${idToken}` } : {}),
        },
        body: JSON.stringify(payload),
      });
      console.log("[checkout] create response status", res.status, res.ok);
      let data: any = null; // eslint-disable-line @typescript-eslint/no-explicit-any
      try {
        data = await res.json();
      } catch (parseErr) {
        console.error("[checkout] response.json() failed", parseErr);
      }
      console.log("[checkout] create response json", data);
      if (!res.ok) {
        if (typeof data?.error === "string" && data.error.toLowerCase().includes("buyerregistrationaddress")) {
          setError("We need your billing address to continue. Please fill in your address, city and postal code.");
          setLoading(false);
          return;
        }
        throw new Error(data?.error || "Payment failed");
      }
      setToken(data?.token);
      setCheckoutHost(data?.checkoutHost || null);

      // Store currency and rate info for display
      if (data?.currency) setPaymentCurrency(data.currency);
      if (data?.usedFxRate) setUsedFxRate(data.usedFxRate);
      if (data?.finalPrice) setFinalPrice(data.finalPrice);

      // Always prefer paymentPageUrl (cpp.iyzipay.com) - it's simpler and more reliable
      if (data?.paymentPageUrl) {
        console.log("[checkout] Using paymentPageUrl", data.paymentPageUrl);
        setPaymentPageUrl(data.paymentPageUrl);
        setCheckoutHtml(`<iframe src="${data.paymentPageUrl}" style="width:100%;height:100%;border:0;"></iframe>`);
      } else if (data?.checkoutFormContent) {
        console.log("[checkout] Fallback to checkoutFormContent, length", data.checkoutFormContent?.length);
        setCheckoutHtml(data.checkoutFormContent);
      } else {
        console.warn("[checkout] No payment URL or form returned, token only", data?.token);
      }
      if (data?.checkoutHost) {
        console.log("[checkout] checkoutHost", data.checkoutHost);
      }
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : "Payment failed";
      console.error("[checkout] createForm error", e);
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  const fetchChannelPreview = async () => {
    if (!channelId) return;

    try {
      const res = await fetch("/api/analysis/preview", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ channelId }),
      });
      const data = await res.json();
      if (res.ok) {
        setChannelPreview(data);
      }
    } catch (error) {
      console.error("Error fetching preview:", error);
    }
  };

  useEffect(() => {
    const qs = new URLSearchParams(window.location.search);
    const st = qs.get("status");
    const planParam = qs.get("plan");
    const channelIdParam = qs.get("channelId");
    const queryIdParam = qs.get("queryId");

    if (st) {
      setError(st === "SUCCESS" ? null : "Payment failed");
    }
    if (planParam && planParam in plans) {
      setPlan(planParam);
    } else if (planParam === 'first') {
      setPlan('first');
    }
    if (channelIdParam) {
      setChannelId(channelIdParam);
    }
    if (queryIdParam) {
      setQueryId(queryIdParam);
    }
  }, []);

  useEffect(() => {
    if (channelId) {
      fetchChannelPreview();
    }
  }, [channelId]);

  const selectedPlan = (plan && plans[plan]) ? plans[plan] : plans.single;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 min-h-screen p-6">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="flex items-center gap-4 mb-12"
          >
            <motion.a
              href="/"
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
              whileHover={{ x: -5 }}
            >
              <ArrowLeft className="w-5 h-5" />
              Back to Home
            </motion.a>
            <a href="/dashboard" className="ml-auto px-3 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg text-sm">Go to Dashboard</a>
            <div className="flex items-center gap-2 ml-auto">
              <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Youtube className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">ytuber</span>
            </div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Plan Selection */}
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="min-w-0"
            >
              <h1 className="text-4xl font-bold text-white mb-2">Choose Your Plan</h1>
              <p className="text-gray-300 mb-8">Select the perfect plan for your YouTube growth journey</p>

              <div className="space-y-4">
                {Object.entries(plans).map(([key, planData]) => {
                  if (!planData) return null;
                  return (
                  <motion.div
                    key={key}
                    className={`relative p-6 rounded-2xl border cursor-pointer transition-all ${
                      plan === key
                        ? 'border-pink-400 bg-white/10 ring-2 ring-pink-400/30'
                        : 'border-white/20 bg-white/5 hover:bg-white/10'
                    }`}
                    onClick={() => setPlan(key)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {('popular' in planData) && (planData as { popular?: boolean }).popular && (
                      <div className="absolute -top-3 left-6">
                        <div className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                          <Star className="w-3 h-3" />
                          Most Popular
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${planData.color || 'from-purple-400 to-pink-500'} rounded-xl flex items-center justify-center`}>
                          <Sparkles className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-white">{planData.name}</h3>
                          {('description' in planData) && (
                            <p className="text-gray-300 text-sm mb-2">{(planData as { description: string }).description}</p>
                          )}
                          <div className="flex items-center gap-2">
                            <span className="text-2xl font-bold text-white">{planData.price}</span>
                            {('originalPrice' in planData) && (planData as { originalPrice?: string }).originalPrice && (
                              <span className="text-sm text-gray-400 line-through">{(planData as { originalPrice?: string }).originalPrice}</span>
                            )}
                            {('badge' in planData) && (planData as { badge?: string }).badge && (
                              <span className="ml-2 text-xs font-semibold px-2 py-0.5 rounded-full bg-green-500/20 text-green-300 border border-green-400/30">{(planData as { badge?: string }).badge}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className={`w-6 h-6 rounded-full border-2 ${
                        plan === key ? 'border-pink-400 bg-pink-400' : 'border-gray-400'
                      } flex items-center justify-center`}>
                        {plan === key && <div className="w-2 h-2 bg-white rounded-full" />}
                      </div>
                    </div>

                    <div className="mt-4 grid grid-cols-2 gap-2">
                      {(planData.features as string[]).map((feature: string, index: number) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                          <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                  );
                })}
              </div>
            </motion.div>
            {/* Right Column: Billing + Summary */}
            <div className="lg:pl-4 space-y-8">
                  {/* Billing Info Form */}
                  <div className="mt-8 space-y-4">
                    <h3 className="text-xl font-semibold text-white">Billing Information</h3>

                    <div className="grid grid-cols-1 gap-4">
                      <div className="grid sm:grid-cols-2 gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Full name" value={billing.fullName} onChange={e=>setBilling({...billing, fullName:e.target.value})} />
                        <input required type="email" className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Email (required)" value={billing.email} onChange={e=>setBilling({...billing, email:e.target.value})} />
                      </div>

                      <div className="grid sm:grid-cols-2 gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Phone (+country code)" value={billing.phone} onChange={e=>setBilling({...billing, phone:e.target.value})} />
                        <select className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white" value={billing.country} onChange={e=>setBilling({...billing, country:e.target.value})}>
                          {COUNTRY_OPTIONS.map(c => (
                            <option key={c.code} value={c.code} className="bg-slate-900 text-white">{c.name}</option>
                          ))}
                        </select>
                      </div>

                      <div className="grid sm:grid-cols-3 gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="State / Region" value={billing.stateRegion} onChange={e=>setBilling({...billing, stateRegion:e.target.value})} />
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="City" value={billing.city} onChange={e=>setBilling({...billing, city:e.target.value})} />
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Postal / ZIP" value={billing.postalCode} onChange={e=>setBilling({...billing, postalCode:e.target.value})} />
                      </div>

                      <div className="grid gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Address line 1" value={billing.address1} onChange={e=>setBilling({...billing, address1:e.target.value})} />
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Address line 2 (optional)" value={billing.address2} onChange={e=>setBilling({...billing, address2:e.target.value})} />
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-2">
                      <label className="text-gray-300 flex items-center gap-2">
                        <input type="checkbox" checked={billing.isBusiness} onChange={e=>setBilling({...billing, isBusiness:e.target.checked})} />
                        Business invoice
                      </label>
                      <span className={`text-xs ${isBillingValid ? 'text-green-300' : 'text-amber-300'}`}>
                        {isBillingValid ? 'Looks good' : 'Please complete your billing information'}
                      </span>
                    </div>

                    {billing.isBusiness && (
                      <div className="grid sm:grid-cols-2 gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Company name" value={billing.companyName} onChange={e=>setBilling({...billing, companyName:e.target.value})} />
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="Tax ID" value={billing.taxId} onChange={e=>setBilling({...billing, taxId:e.target.value})} />
                      </div>
                    )}

                    {billing.country === 'TR' && !billing.isBusiness && (
                      <div className="grid sm:grid-cols-2 gap-4">
                        <input className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400" placeholder="National ID (TCKN) – required in Turkey" value={billing.nationalId} onChange={e=>setBilling({...billing, nationalId:e.target.value})} />
                        <div className="text-sm text-gray-400 flex items-center">TCKN is required for individual invoices in Turkey.</div>
                      </div>
                    )}
                  </div>


            {/* Payment Section */}
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className=""
            >
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-6 lg:p-8">
                <h2 className="text-2xl font-bold text-white mb-2">Order Summary</h2>
                <p className="text-gray-300 mb-6 text-sm">Review your plan and billing details, then complete your purchase.</p>

                {/* Selected Plan Summary */}
                <div className="bg-white/5 rounded-2xl p-6 mb-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${selectedPlan?.color ?? 'from-purple-400 to-pink-500'} rounded-xl flex items-center justify-center`}>
                      <Sparkles className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{selectedPlan?.name ?? 'Complete Analysis'}</h3>
                      <div className="space-y-1">
                        <p className="text-gray-300">{selectedPlan?.price ?? '$4.99'}</p>
                        {paymentCurrency === 'TRY' && finalPrice && usedFxRate && (
                          <div className="text-sm text-green-300">
                            ≈ {finalPrice} TRY
                            <span className="text-xs text-gray-400 ml-2">
                              (Kur: {usedFxRate.toFixed(4)})
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {(selectedPlan?.features ?? plans.single.features).map((feature: string, index: number) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Security badges */}
                <div className="flex items-center gap-4 mb-6 text-sm text-gray-300">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-green-400" />
                    Secure Payment
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCard className="w-4 h-4 text-blue-400" />
                    SSL Protected
                  </div>
                </div>

                {/* Payment Button */}
                <motion.button
                  onClick={createForm}
                  disabled={loading}
                  className="w-full py-4 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-2xl font-semibold shadow-lg shadow-pink-500/25 hover:shadow-xl hover:shadow-pink-500/40 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Processing...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <Zap className="w-5 h-5" />
                      Complete Payment
                    </div>
                  )}
                </motion.button>

                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-xl text-red-400"
                    >
                      {error}
                    </motion.div>
                  )}
                </AnimatePresence>

                {(checkoutHtml || token) && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="mt-6"
                  >
                    {(secureReachable === false || forceCpp) && paymentPageUrl ? (
                      <iframe src={paymentPageUrl} className="w-full h-[600px] border border-white/20 rounded-2xl" />
                    ) : checkoutHtml && hasValidCheckoutForm ? (
                      <div className="w-full h-[600px] border border-white/20 rounded-2xl bg-white" dangerouslySetInnerHTML={{ __html: checkoutHtml }} />
                    ) : paymentPageUrl ? (
                      <iframe src={paymentPageUrl} className="w-full h-[600px] border border-white/20 rounded-2xl" />
                    ) : token ? (
                      <iframe
                        src={`${checkoutHost || 'https://secure.iyzipay.com'}/threeds/initialize/${token}`}
                        className="w-full h-[600px] border border-white/20 rounded-2xl"
                      />
                    ) : checkoutHtml ? (
                      <div className="w-full h-[220px] border border-red-500/30 bg-red-500/10 rounded-2xl flex flex-col items-center justify-center text-red-300 gap-3 px-4 text-center">
                        <div>Payment form could not be loaded.</div>
                        {token ? (
                          <a
                            href={`${checkoutHost || 'https://secure.iyzipay.com'}/threeds/initialize/${token}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-red-200 underline hover:text-white"
                          >
                            Open payment in a new window
                          </a>
                        ) : null}
                        <div className="text-xs text-red-200/80">
                          If it still fails, disable VPN/ad-block, try another browser/network, and ensure secure.iyzipay.com is reachable.
                        </div>
                      </div>
                    ) : null}
                  </motion.div>
                )}

	                {paymentPageUrl && (
	                  <div className="mt-3 text-sm text-gray-300">
	                    Having trouble?{' '}
	                    <a
	                      href={paymentPageUrl}
	                      target="_blank"
	                      rel="noopener noreferrer"
	                      className="text-pink-300 underline hover:text-white"
	                    >
	                      Open payment in a new window
	                    </a>
	                  </div>
	                )}

              </div>
            </motion.div>
          </div>
        </div>
      </div>

        </div>
    </div>
  );
}


// Disable SSG for this page
export default dynamic(() => Promise.resolve(CheckoutPage), {
  ssr: false
});


