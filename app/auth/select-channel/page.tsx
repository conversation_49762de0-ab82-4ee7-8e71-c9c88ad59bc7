"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Youtube, Users, Video, CheckCircle, ArrowRight } from "lucide-react";

interface YouTubeChannel {
  id: string;
  title: string;
  thumbnail: string;
  subscriberCount: string;
  videoCount: string;
}

interface SessionData {
  user: {
    id: string;
    email: string;
    name: string;
    picture: string;
  };
  tokens: {
    access_token: string;
    refresh_token: string;
    expiry_date: number;
  };
  channels: YouTubeChannel[];
}

function SelectChannelContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [channelUrl, setChannelUrl] = useState("");
  const [queryId, setQueryId] = useState("");
  const [firebaseReady, setFirebaseReady] = useState(false);
  const [debugInfo, setDebugInfo] = useState<{requestId: string, error: string, debugStages: string[]} | null>(null);

  useEffect(() => {
    const url = searchParams.get("url");
    const id = searchParams.get("queryId");
    
    if (url) setChannelUrl(decodeURIComponent(url));
    if (id) setQueryId(id);

    // Get session data from cookie
    fetchSessionData();

    // Ensure Firebase client is signed-in with custom token so AuthContext has user
    (async () => {
      try {
        const res = await fetch('/api/auth/firebase/token');
        if (res.ok) {
          const { customToken } = await res.json();
          const { getAuth, signInWithCustomToken } = await import('firebase/auth');
          const { auth } = await import('@/lib/firebase/client');
          await signInWithCustomToken(auth as unknown as ReturnType<typeof getAuth>, customToken);
          setFirebaseReady(true);
        }
      } catch (e) {
        console.warn('Firebase client sign-in skipped:', e);
      }
    })();
  }, [searchParams]);

  const fetchSessionData = async () => {
    try {
      const response = await fetch("/api/auth/session");
      if (response.ok) {
        const data = await response.json();
        setSessionData(data);
      } else {
        // If no session (likely expired Google tokens), jump directly to Google OAuth
        const target = `/auth/select-channel?url=${encodeURIComponent(channelUrl)}&queryId=${queryId}`;
        router.push(`/api/auth/google?callbackUrl=${encodeURIComponent(target)}`);
      }
    } catch (error) {
      console.error("Error fetching session:", error);
      const target = `/auth/select-channel?url=${encodeURIComponent(channelUrl)}&queryId=${queryId}`;
      router.push(`/api/auth/google?callbackUrl=${encodeURIComponent(target)}`);
    } finally {
      setLoading(false);
    }
  };

  const handleChannelSelect = async () => {
    if (!selectedChannel || !sessionData) return;

    setSubmitting(true);
    try {
      // Save YouTube connection
      await fetch("/api/auth/youtube/connect", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          channelId: selectedChannel,
          tokens: sessionData.tokens,
          queryId,
        }),
      });

      // Track channel selection
      if (queryId) {
        await fetch("/api/track/channel-selected", {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ queryId, channelId: selectedChannel }),
        });
      }

      // Eğer kredi yoksa checkout'a yönlendir; varsa kullanıcıdan onay iste ve ardından çalıştır
      try {
        const { getAuth } = await import('firebase/auth');
        const { auth } = await import('@/lib/firebase/client');
        const idToken = await (auth as ReturnType<typeof getAuth>).currentUser?.getIdToken();
        const packInfoRes = idToken ? await fetch('/api/user/pack-info', { headers: { Authorization: `Bearer ${idToken}` } }) : null;
        const packInfo = packInfoRes?.ok ? await packInfoRes.json() : null;
        const credits = packInfo?.packCredits ?? 0;
        if (credits <= 0) {
          router.push(`/checkout?url=${encodeURIComponent(channelUrl)}&channelId=${selectedChannel}&queryId=${queryId}`);
          return;
        }

        const proceed = typeof window !== 'undefined' ? window.confirm('1 kredi kullanarak raporu şimdi oluşturmak istiyor musunuz?') : true;
        if (!proceed) {
          router.push('/dashboard');
          return;
        }

        const res = await fetch('/api/analysis/full', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(idToken ? { Authorization: `Bearer ${idToken}` } : {})
          },
          body: JSON.stringify({ channelUrl, channelId: selectedChannel, queryId })
        });

        if (res.ok) {
          const report = await res.json();
          if (report?.id) {
            router.push(`/dashboard/reports/${report.id}`);
          } else {
            router.push('/dashboard');
          }
        } else if (res.status === 402) {
          router.push(`/checkout?url=${encodeURIComponent(channelUrl)}&channelId=${selectedChannel}&queryId=${queryId}`);
        } else if (res.status === 404) {
          alert('YouTube bağlantısı bulunamadı. Lütfen tekrar bağlayın.');
          router.push(`/auth/signin?url=${encodeURIComponent(channelUrl)}&queryId=${queryId}`);
        } else {
          // Capture detailed error information for debugging
          try {
            const errorData = await res.json();
            console.error('Analysis API Error:', {
              status: res.status,
              requestId: errorData.requestId,
              error: errorData.error,
              debugStages: errorData.debugStages
            });

            // Store debug info for display
            setDebugInfo({
              requestId: errorData.requestId,
              error: errorData.error,
              debugStages: errorData.debugStages || []
            });

            // Show detailed error to user for debugging
            const debugInfoText = errorData.debugStages ? `\n\nDebug Info:\nRequest ID: ${errorData.requestId}\nStages completed: ${errorData.debugStages.join(' → ')}\nError: ${errorData.error}` : '';
            alert(`Rapor oluşturulamadı (${res.status}). Lütfen bu bilgileri geliştiriciye iletin:${debugInfoText}`);
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError);
            alert(`Rapor oluşturulamadı (${res.status}). Lütfen tekrar deneyin.`);
          }
          router.push('/dashboard');
        }
      } catch (e) {
        console.error(e);
        router.push('/dashboard');
      }
    } catch (error) {
      console.error("Error selecting channel:", error);
      setSubmitting(false);
    }
  };

  const formatNumber = (num: string) => {
    const number = parseInt(num);
    if (number >= 1000000) {
      return `${(number / 1000000).toFixed(1)}M`;
    } else if (number >= 1000) {
      return `${(number / 1000).toFixed(1)}K`;
    }
    return number.toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading your channels...</p>
        </div>
      </div>
    );
  }

  if (!sessionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-white">Session expired. Redirecting...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Youtube className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Select Your Channel
            </h1>
            <p className="text-xl text-gray-300">
              Which channel would you like to analyze?
            </p>
            <div className="mt-4 text-sm text-gray-400">
              Analyzing: <span className="text-white font-medium">{channelUrl}</span>
            </div>
          </motion.div>

          {/* User Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 mb-8"
          >
            <div className="flex items-center gap-4">
              <img
                src={sessionData.user.picture}
                alt={sessionData.user.name}
                className="w-12 h-12 rounded-full"
              />
              <div>
                <h3 className="text-white font-semibold">{sessionData.user.name}</h3>
                <p className="text-gray-300 text-sm">{sessionData.user.email}</p>
              </div>
            </div>
          </motion.div>

          {/* Channel Selection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4 mb-8"
          >
            {sessionData.channels.map((channel, index) => (
              <motion.div
                key={channel.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index }}
                className={`bg-white/10 backdrop-blur-sm border-2 rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:bg-white/20 ${
                  selectedChannel === channel.id
                    ? "border-red-500 bg-red-500/10"
                    : "border-white/20"
                }`}
                onClick={() => setSelectedChannel(channel.id)}
              >
                <div className="flex items-center gap-6">
                  <img
                    src={channel.thumbnail}
                    alt={channel.title}
                    className="w-16 h-16 rounded-xl"
                  />
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {channel.title}
                    </h3>
                    <div className="flex items-center gap-6 text-gray-300">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        <span className="text-sm">
                          {formatNumber(channel.subscriberCount)} subscribers
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Video className="w-4 h-4" />
                        <span className="text-sm">
                          {formatNumber(channel.videoCount)} videos
                        </span>
                      </div>
                    </div>
                  </div>
                  {selectedChannel === channel.id && (
                    <CheckCircle className="w-8 h-8 text-red-400" />
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Debug Info Display */}
          {debugInfo && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-500/10 border border-red-500/30 rounded-2xl p-6 mb-8"
            >
              <h3 className="text-red-400 font-semibold mb-4">Debug Information (Please share with developer)</h3>
              <div className="space-y-2 text-sm font-mono">
                <div className="text-gray-300">
                  <span className="text-red-400">Request ID:</span> {debugInfo.requestId}
                </div>
                <div className="text-gray-300">
                  <span className="text-red-400">Error:</span> {debugInfo.error}
                </div>
                <div className="text-gray-300">
                  <span className="text-red-400">Stages Completed:</span> {debugInfo.debugStages.join(' → ')}
                </div>
              </div>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(debugInfo, null, 2));
                  alert('Debug info copied to clipboard!');
                }}
                className="mt-4 bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg text-sm transition-colors"
              >
                Copy Debug Info
              </button>
            </motion.div>
          )}

          {/* Continue Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="text-center"
          >
            <button
              onClick={handleChannelSelect}
              disabled={!selectedChannel || submitting}
              className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:shadow-lg hover:shadow-red-500/25 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3 mx-auto"
            >
              {submitting ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  Continue to Analysis
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export default function SelectChannelPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    }>
      <SelectChannelContent />
    </Suspense>
  );
}
