"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Youtube, Shield, BarChart3, Target, ArrowRight } from "lucide-react";
import { useAuth } from "@/app/providers/AuthContext";
// import { signIn } from "next-auth/react"; // We use Firebase Auth instead

function SignInContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [channelUrl, setChannelUrl] = useState("");
  const [queryId, setQueryId] = useState("");

  useEffect(() => {
    const url = searchParams.get("url");
    const id = searchParams.get("queryId");

    if (url) {
      setChannelUrl(decodeURIComponent(url));
    }
    if (id) {
      setQueryId(id);
    }
  }, [searchParams]);

  // If user already has a selected channel in DB, skip OAuth and go to dashboard/checkout
  useEffect(() => {
    (async () => {
      try {
        if (!user) return;
        const token = await user.getIdToken();
        const res = await fetch("/api/user/selected-channel", {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!res.ok) return;
        const data = await res.json();
        if (data?.selectedChannelId) {
          if (channelUrl) {
            router.replace(`/checkout?url=${encodeURIComponent(channelUrl)}&channelId=${data.selectedChannelId}&queryId=${queryId}`);
          } else {
            router.replace("/dashboard");
          }
        }
      } catch {
        // yoksa normal akışa devam
      }
    })();
  }, [user, channelUrl, queryId, router]);

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      // Optional: Track signin started (non-blocking)
      if (queryId) {
        try {
          await fetch("/api/track/signin-started", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ queryId }),
          });
        } catch (trackingError) {
          console.log("Tracking failed (non-critical):", trackingError);
        }
      }

      // Build Google OAuth URL directly to avoid Netlify API 404
      const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "";
      const redirectUri = encodeURIComponent(`${window.location.origin}/api/auth/google/callback`);
      const scope = encodeURIComponent([
        'openid',
        'email',
        'profile',
        'https://www.googleapis.com/auth/youtube.readonly',
        'https://www.googleapis.com/auth/yt-analytics.readonly'
      ].join(' '));
      const state = encodeURIComponent(`/auth/select-channel?url=${encodeURIComponent(channelUrl)}&queryId=${queryId}`);

      const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&access_type=offline&include_granted_scopes=true&state=${state}`;

      window.location.href = googleAuthUrl;
    } catch (error) {
      console.error("Sign-in error:", error);
      setLoading(false);
    }
  };

  const extractChannelName = (url: string) => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');

      if (pathParts.includes('channel')) {
        return pathParts[pathParts.indexOf('channel') + 1];
      } else if (pathParts.includes('c')) {
        return pathParts[pathParts.indexOf('c') + 1];
      } else if (pathParts.includes('@')) {
        return pathParts.find(part => part.startsWith('@'));
      }

      return "your channel";
    } catch {
      return "your channel";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Youtube className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Connect Your YouTube Channel
            </h1>
            <p className="text-xl text-gray-300">
              To provide accurate analysis for <span className="text-white font-semibold">{extractChannelName(channelUrl)}</span>,
              we need access to your YouTube Analytics data.
            </p>
          </motion.div>

          {/* Why we need this */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 mb-8"
          >
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center gap-3">
              <Shield className="w-6 h-6 text-blue-400" />
              Why we need YouTube Analytics access:
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <BarChart3 className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium">Real Performance Data</h3>
                    <p className="text-gray-300 text-sm">Actual views, watch time, and engagement metrics instead of estimates</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Target className="w-5 h-5 text-purple-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium">Personalized Insights</h3>
                    <p className="text-gray-300 text-sm">Recommendations tailored to your specific audience and content</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <BarChart3 className="w-5 h-5 text-yellow-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium">Competitor Analysis</h3>
                    <p className="text-gray-300 text-sm">Compare your performance with similar channels in your niche</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Target className="w-5 h-5 text-red-400 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium">Growth Optimization</h3>
                    <p className="text-gray-300 text-sm">Data-driven strategies to increase views, subscribers, and revenue</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Security note */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-green-500/10 border border-green-500/20 rounded-xl p-6 mb-8"
          >
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-green-400 font-medium mb-2">Secure & Read-Only Access</h3>
                <p className="text-gray-300 text-sm">
                  We only request read-only permissions to view your analytics data.
                  We cannot upload, edit, or delete any of your content. You can revoke access anytime.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Sign in button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="text-center"
          >
            <button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:shadow-lg hover:shadow-red-500/25 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3 mx-auto"
            >
              {loading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Youtube className="w-5 h-5" />
                  Continue with Google
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>

            <p className="text-gray-400 text-sm mt-4">
              By continuing, you agree to our Terms of Service and Privacy Policy
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    }>
      <SignInContent />
    </Suspense>
  );
}
