"use client";

import { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>ircle, ArrowLeft, Settings } from "lucide-react";
import Link from "next/link";

function ErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case "oauth_not_configured":
        return {
          title: "OAuth Setup Required",
          message: "Google OAuth credentials need to be configured to enable YouTube channel analysis.",
          suggestion: "To set up OAuth: 1) Go to Google Cloud Console → APIs & Credentials, 2) Create OAuth 2.0 Client ID for Web Application, 3) Add redirect URI: http://localhost:3000/api/auth/google/callback, 4) Add credentials to .env.local file"
        };
      case "oauth_failed":
        return {
          title: "Authentication Failed",
          message: "Failed to initiate Google authentication. Please try again.",
          suggestion: "If the problem persists, please contact support."
        };
      case "callback_failed":
        return {
          title: "Authentication Callback Failed",
          message: "There was an error processing your authentication. Please try again.",
          suggestion: "Make sure you're using a supported browser and have cookies enabled."
        };
      case "no_code":
        return {
          title: "Authentication Incomplete",
          message: "The authentication process was not completed properly.",
          suggestion: "Please try signing in again and make sure to complete the Google authorization process."
        };
      default:
        return {
          title: "Authentication Error",
          message: "An unexpected error occurred during authentication.",
          suggestion: "Please try again or contact support if the problem persists."
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center"
        >
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <AlertCircle className="w-8 h-8 text-red-400" />
          </motion.div>

          {/* Error Title */}
          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-2xl font-bold text-white mb-4"
          >
            {errorInfo.title}
          </motion.h1>

          {/* Error Message */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-gray-300 mb-4"
          >
            {errorInfo.message}
          </motion.p>

          {/* Suggestion */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4 mb-6"
          >
            <div className="flex items-start gap-3">
              <Settings className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <p className="text-blue-300 text-sm text-left">
                {errorInfo.suggestion}
              </p>
            </div>
          </motion.div>

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="space-y-3"
          >
            <Link
              href="/"
              className="block w-full py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-medium hover:shadow-lg hover:shadow-red-500/25 transition-all"
            >
              <div className="flex items-center justify-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </div>
            </Link>
            
            <button
              onClick={() => window.location.reload()}
              className="block w-full py-3 bg-white/10 text-white rounded-xl font-medium hover:bg-white/20 transition-all"
            >
              Try Again
            </button>
          </motion.div>

          {/* Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && error && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="mt-6 p-3 bg-gray-800/50 rounded-lg"
            >
              <p className="text-xs text-gray-400">
                Debug: Error code = {error}
              </p>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    }>
      <ErrorContent />
    </Suspense>
  );
}
