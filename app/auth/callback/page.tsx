"use client";

import { Suspense, useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react";

function CallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          setStatus('error');
          setMessage(`Authentication failed: ${error}`);
          return;
        }

        if (!code) {
          setStatus('error');
          setMessage('No authorization code received');
          return;
        }

        // Call the real API callback
        setMessage('Processing authentication...');

        const response = await fetch(`/api/auth/google/callback?code=${code}&state=${encodeURIComponent(state || '')}`, {
          method: 'GET',
        });

        if (response.ok) {
          // After Google OAuth, ensure Firebase Auth user exists by minting a custom token
          try {
            const tokenRes = await fetch('/api/auth/firebase/token');
            if (tokenRes.ok) {
              const { customToken } = await tokenRes.json();
              // Lazy import firebase auth on client
              const { getAuth, signInWithCustomToken } = await import('firebase/auth');
              const { auth } = await import('@/lib/firebase/client');
              await signInWithCustomToken(auth as unknown as ReturnType<typeof getAuth>, customToken);
            }
          } catch (e) {
            console.warn('Firebase custom sign-in skipped:', e);
          }

          setStatus('success');
          setMessage('Authentication successful! Redirecting...');

          // Extract callback URL from state
          const callbackUrl = state || '/auth/select-channel';

          setTimeout(() => {
            router.push(callbackUrl);
          }, 800);
        } else {
          setStatus('error');
          setMessage('Authentication failed. Please try again.');
        }

      } catch (error) {
        console.error('Callback error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred during authentication');
      }
    };

    handleCallback();
  }, [searchParams, router]);

  const getIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-8 h-8 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-8 h-8 text-red-400" />;
    }
  };

  const getColor = () => {
    switch (status) {
      case 'processing':
        return 'from-blue-500/20 to-purple-500/20 border-blue-500/20';
      case 'success':
        return 'from-green-500/20 to-blue-500/20 border-green-500/20';
      case 'error':
        return 'from-red-500/20 to-orange-500/20 border-red-500/20';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`bg-white/10 backdrop-blur-sm border rounded-3xl p-8 text-center ${getColor()}`}
        >
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="flex justify-center mb-6"
          >
            {getIcon()}
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-2xl font-bold text-white mb-4"
          >
            {status === 'processing' && 'Authenticating...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Authentication Failed'}
          </motion.h1>

          {/* Message */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-gray-300 mb-6"
          >
            {message}
          </motion.p>

          {/* Action Button */}
          {status === 'error' && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              onClick={() => router.push('/')}
              className="w-full py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-medium hover:shadow-lg hover:shadow-red-500/25 transition-all"
            >
              Back to Home
            </motion.button>
          )}

          {/* Loading indicator for success */}
          {status === 'success' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="flex items-center justify-center gap-2 text-green-400"
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Redirecting...</span>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  );
}
