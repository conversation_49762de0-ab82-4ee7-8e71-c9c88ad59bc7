import { Metadata } from 'next';
import Link from 'next/link';
import { TrendingUp, Users, BarChart3, ArrowRight, Award } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Growth Success Stories & Case Studies - YTuber',
  description: 'Explore detailed case studies of YouTube creators who achieved massive growth using YTuber analytics. Learn from real data and proven strategies.',
  keywords: 'YouTube growth case studies, creator success stories, YouTube analytics results, channel growth examples, YouTube success metrics',
  openGraph: {
    title: 'YouTube Growth Success Stories & Case Studies - YTuber',
    description: 'Explore detailed case studies of YouTube creators who achieved massive growth using YTuber analytics.',
    url: 'https://ytuber.life/success-stories',
    type: 'website',
  },
  other: {
    canonical: 'https://ytuber.life/success-stories',
  },
};

const caseStudies = [
  {
    id: 1,
    title: "From 10K to 1M Subscribers in 8 Months",
    creator: "TechReview Pro",
    category: "Technology",
    timeframe: "8 months",
    featured: true,
    thumbnail: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop",
    metrics: {
      subscribers: { before: "10K", after: "1M", growth: "+9,900%" },
      monthlyViews: { before: "50K", after: "8.5M", growth: "+16,900%" },
      revenue: { before: "$200", after: "$35K", growth: "+17,400%" },
      engagement: { before: "1.2%", after: "6.8%", growth: "+467%" }
    },
    keyInsights: [
      "Optimized upload schedule based on audience analytics",
      "Improved thumbnail CTR from 2.1% to 12.3%",
      "Focused on trending tech topics with low competition",
      "Created series content to boost session duration"
    ],
    challenge: "Low engagement and inconsistent growth despite quality content",
    solution: "Used YTuber's algorithm insights to identify optimal posting times and trending topics",
    results: "Achieved viral growth with 3 videos hitting 1M+ views each"
  },
  {
    id: 2,
    title: "Cooking Channel's Recipe for Success",
    creator: "Flavor Adventures",
    category: "Food & Cooking",
    timeframe: "6 months",
    featured: true,
    thumbnail: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop",
    metrics: {
      subscribers: { before: "25K", after: "750K", growth: "+2,900%" },
      monthlyViews: { before: "100K", after: "4.2M", growth: "+4,100%" },
      revenue: { before: "$500", after: "$18K", growth: "+3,500%" },
      engagement: { before: "2.8%", after: "8.1%", growth: "+189%" }
    },
    keyInsights: [
      "Discovered audience preferred quick 5-minute recipes",
      "Seasonal content planning increased views by 340%",
      "Collaboration strategy boosted subscriber growth",
      "Shorts integration drove 60% of new subscribers"
    ],
    challenge: "Stagnant growth in competitive cooking niche",
    solution: "Leveraged YTuber's content gap analysis to find underserved recipe categories",
    results: "Became top 3 cooking channel in their region within 6 months"
  },
  {
    id: 3,
    title: "Gaming Channel's Comeback Story",
    creator: "Epic Gaming Hub",
    category: "Gaming",
    timeframe: "12 months",
    featured: false,
    thumbnail: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=600&h=400&fit=crop",
    metrics: {
      subscribers: { before: "5K", after: "500K", growth: "+9,900%" },
      monthlyViews: { before: "20K", after: "3.1M", growth: "+15,400%" },
      revenue: { before: "$0", after: "$12K", growth: "∞" },
      engagement: { before: "0.8%", after: "5.2%", growth: "+550%" }
    },
    keyInsights: [
      "Pivoted to trending games using YTuber's trend analysis",
      "Optimized video length for maximum retention",
      "Built community through live streaming integration",
      "Cross-platform promotion strategy"
    ],
    challenge: "Channel was declining with outdated game content",
    solution: "Complete content strategy overhaul based on YTuber analytics",
    results: "Transformed from declining channel to top gaming creator"
  },
  {
    id: 4,
    title: "Fitness Transformation Success",
    creator: "FitLife Journey",
    category: "Health & Fitness",
    timeframe: "10 months",
    featured: false,
    thumbnail: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop",
    metrics: {
      subscribers: { before: "15K", after: "650K", growth: "+4,233%" },
      monthlyViews: { before: "75K", after: "2.8M", growth: "+3,633%" },
      revenue: { before: "$300", after: "$22K", growth: "+7,233%" },
      engagement: { before: "1.5%", after: "7.3%", growth: "+387%" }
    },
    keyInsights: [
      "Workout series format increased binge-watching",
      "Before/after content performed exceptionally well",
      "Community challenges boosted engagement",
      "Nutrition content filled content gaps"
    ],
    challenge: "Difficulty standing out in saturated fitness market",
    solution: "Used YTuber's competitor analysis to find unique positioning",
    results: "Built loyal community with 73% returning viewers"
  }
];

const overallStats = [
  { label: "Total Creators Analyzed", value: "50,000+", icon: Users },
  { label: "Combined Subscriber Growth", value: "2.3B+", icon: TrendingUp },
  { label: "Average Growth Rate", value: "347%", icon: BarChart3 },
  { label: "Success Rate", value: "94%", icon: Award }
];

export default function SuccessStoriesPage() {
  const featuredStories = caseStudies.filter(story => story.featured);
  const regularStories = caseStudies.filter(story => !story.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'Success Stories', href: '/success-stories' }]}
            className="mb-8"
          />

          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-white">
                Success Stories & Case Studies
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Dive deep into real YouTube growth transformations. See exactly how creators used 
              YTuber analytics to achieve extraordinary results in 2025.
            </p>
            
            {/* Overall Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {overallStats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                    <div className="text-gray-400 text-sm">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Featured Case Studies */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">Featured Case Studies</h2>
            <div className="space-y-12">
              {featuredStories.map((story) => (
                <CaseStudyCard key={story.id} story={story} detailed={true} />
              ))}
            </div>
          </div>

          {/* More Success Stories */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">More Success Stories</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {regularStories.map((story) => (
                <CaseStudyCard key={story.id} story={story} detailed={false} />
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Create Your Success Story?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                These creators started where you are now. With the right analytics and strategy, 
                your channel could be our next featured success story.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold rounded-xl hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Start Your Analysis
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/testimonials"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Read More Testimonials
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface CaseStudyType {
  id: number;
  title: string;
  creator: string;
  category: string;
  timeframe: string;
  featured: boolean;
  thumbnail: string;
  metrics: Record<string, { before: string; after: string; growth: string }>;
  keyInsights: string[];
  challenge: string;
  solution: string;
  results: string;
}

function CaseStudyCard({ story, detailed }: { story: CaseStudyType; detailed: boolean }) {
  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/10">
      {/* Header with Image */}
      <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
        <img 
          src={story.thumbnail} 
          alt={story.title}
          className="w-full h-full object-cover opacity-80"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute bottom-4 left-6 right-6">
          <div className="flex items-center gap-2 mb-2">
            <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm">
              {story.category}
            </span>
            <span className="px-3 py-1 bg-green-500/20 backdrop-blur-sm rounded-full text-green-400 text-sm">
              {story.timeframe}
            </span>
          </div>
          <h3 className="text-xl font-bold text-white">{story.title}</h3>
          <p className="text-gray-300 text-sm">{story.creator}</p>
        </div>
      </div>

      <div className="p-6">
        {/* Metrics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {Object.entries(story.metrics).map(([key, metric]) => (
            <div key={key} className="bg-white/5 rounded-lg p-3 text-center">
              <div className="text-xs text-gray-400 mb-1 capitalize">
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </div>
              <div className="text-sm text-white font-medium">
                {metric.before} → {metric.after}
              </div>
              <div className="text-xs text-green-400">{metric.growth}</div>
            </div>
          ))}
        </div>

        {detailed && (
          <>
            {/* Challenge & Solution */}
            <div className="space-y-4 mb-6">
              <div>
                <h4 className="text-sm font-semibold text-red-400 mb-2">Challenge:</h4>
                <p className="text-gray-300 text-sm">{story.challenge}</p>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-blue-400 mb-2">Solution:</h4>
                <p className="text-gray-300 text-sm">{story.solution}</p>
              </div>
              <div>
                <h4 className="text-sm font-semibold text-green-400 mb-2">Results:</h4>
                <p className="text-gray-300 text-sm">{story.results}</p>
              </div>
            </div>

            {/* Key Insights */}
            <div>
              <h4 className="text-sm font-semibold text-white mb-3">Key Insights:</h4>
              <ul className="space-y-2">
                {story.keyInsights.map((insight: string, index: number) => (
                  <li key={index} className="flex items-start gap-2 text-gray-300 text-sm">
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                    {insight}
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
