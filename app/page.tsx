"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "./providers/AuthContext";
import { signInWithGoogle, signOut } from "@/lib/auth";
import { useABTest, usePerformanceMonitoring } from "@/hooks/useABTest";
import Image from "next/image";
import Link from "next/link";
import { softwareApplicationSchema, organizationSchema } from "@/lib/seo/schemas";
import {
  Play,
  TrendingUp,
  Zap,
  Target,
  BarChart3,
  Sparkles,
  CheckCircle,
  ArrowRight,
  Youtube,
  Eye,
  Users,
  Calendar,
  Star,
  Menu,
  X
} from "lucide-react";

export default function Home() {
  const { user } = useAuth();
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState<Record<string, unknown> | null>(null);
  const [error, setError] = useState<string | null>(null);

  // A/B Testing for CTA button text
  const { variant: ctaVariant, trackConversion } = useABTest({
    testId: "cta_button_text",
    userId: user?.uid || "anonymous",
    defaultVariant: "control"
  });

  // Performance monitoring
  const { trackCustomEvent } = usePerformanceMonitoring();
  const [quota, setQuota] = useState<{ used: number; limit: number } | null>(null);
  const [isUrlFocused, setIsUrlFocused] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [urlError, setUrlError] = useState<string | null>(null);

  useEffect(() => {
    // Mock quota data to avoid API dependency
    setQuota({
      used: Math.floor(Math.random() * 80) + 10, // Random between 10-90
      limit: 100
    });
  }, []);

  const validateUrl = (url: string): string | null => {
    if (!url.trim()) return "Please enter a YouTube channel URL";

    const youtubePatterns = [
      /^https?:\/\/(www\.)?youtube\.com\/@[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/channel\/[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/c\/[\w-]+/,
      /^https?:\/\/(www\.)?youtube\.com\/user\/[\w-]+/
    ];

    const isValidYouTubeUrl = youtubePatterns.some(pattern => pattern.test(url));
    if (!isValidYouTubeUrl) {
      return "Please enter a valid YouTube channel URL (e.g., https://www.youtube.com/@channel)";
    }

    return null;
  };

  const onPreview = async () => {
    setError(null);
    setUrlError(null);

    const validationError = validateUrl(url);
    if (validationError) {
      setUrlError(validationError);
      return;
    }

    setLoading(true);
    try {
      // Generate a simple query ID without API call
      const queryId = `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Optional: Track events (non-blocking)
      try {
        await trackConversion();
        await trackCustomEvent("cta_clicked", {
          variant: ctaVariant,
          channelUrl: url,
          userType: user ? "authenticated" : "anonymous"
        });
      } catch (trackingError) {
        // Ignore tracking errors, don't block the main flow
        console.log("Tracking failed (non-critical):", trackingError);
      }

      // Redirect to sign-in page with URL and query ID
      // This will start the OAuth flow: Sign-in → Channel Selection → Payment
      window.location.href = `/auth/signin?url=${encodeURIComponent(url)}&queryId=${queryId}`;
    } catch (e: unknown) {
      console.error("Preview error:", e);
      const message = e instanceof Error ? e.message : "Failed to process request";
      setError(message);
      setLoading(false);
    }
  };

  const usedPct = quota ? Math.min(100, (quota.used / quota.limit) * 100) : 0;

  return (
    <>
      {/* Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareApplicationSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Grid pattern overlay */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage:
            `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 max-w-7xl mx-auto px-6 py-6 flex items-center justify-between"
      >
        <motion.div
          className="flex items-center gap-2"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
            <Youtube className="w-5 h-5 text-white" />
          </div>
          <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            ytuber
          </span>
        </motion.div>

        <nav className="hidden md:flex items-center gap-8 text-sm">
          <motion.a
            href="#features"
            className="text-gray-300 hover:text-white transition-colors"
            whileHover={{ y: -2 }}
          >
            Features
          </motion.a>
          <motion.a
            href="#pricing"
            className="text-gray-300 hover:text-white transition-colors"
            whileHover={{ y: -2 }}
          >
            Pricing
          </motion.a>
          <motion.a
            href="/blog"
            className="text-gray-300 hover:text-white transition-colors"
            whileHover={{ y: -2 }}
          >
            Growth Blog
          </motion.a>
          <motion.a
            href="/youtube-growth"
            className="text-gray-300 hover:text-white transition-colors"
            whileHover={{ y: -2 }}
          >
            YouTube Growth
          </motion.a>

          {user && (
            <div className="flex items-center gap-4">
              <motion.a
                href="/dashboard"
                className="text-gray-300 hover:text-white transition-colors"
                whileHover={{ y: -2 }}
              >
                Dashboard
              </motion.a>
              <div className="flex items-center gap-3">
                <Image
                  src={user.photoURL || '/default-avatar.svg'}
                  alt={user.displayName || 'User'}
                  width={32}
                  height={32}
                  className="w-8 h-8 rounded-full"
                />
                <motion.button
                  onClick={() => signOut()}
                  className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
                  whileHover={{ y: -2 }}
                >
                  Sign Out
                </motion.button>
              </div>
            </div>
          )}
        </nav>

        {/* Mobile menu button */}
        <motion.button
          className="md:hidden text-white p-2"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          whileTap={{ scale: 0.95 }}
        >
          {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </motion.button>
      </motion.header>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden relative z-20 bg-black/90 backdrop-blur-sm border-b border-white/10"
          >
            <div className="max-w-7xl mx-auto px-6 py-4 space-y-4">
              <a href="#features" className="block text-gray-300 hover:text-white transition-colors py-2">Features</a>
              <a href="#pricing" className="block text-gray-300 hover:text-white transition-colors py-2">Pricing</a>
              <Link href="/blog" className="block text-gray-300 hover:text-white transition-colors py-2">Growth Blog</Link>
              <a href="/youtube-growth" className="block text-gray-300 hover:text-white transition-colors py-2">YouTube Growth</a>

              {user && (
                <>
                  <a href="/dashboard" className="block text-gray-300 hover:text-white transition-colors py-2">Dashboard</a>
                  <div className="flex items-center gap-3 py-2">
                    <Image
                      src={user.photoURL || '/default-avatar.svg'}
                      alt={user.displayName || 'User'}
                      width={24}
                      height={24}
                      className="w-6 h-6 rounded-full"
                    />
                    <span className="text-gray-300 text-sm">{user.displayName}</span>
                  </div>
                  <button
                    onClick={() => signOut()}
                    className="block w-full text-left text-gray-300 hover:text-white transition-colors py-2"
                  >
                    Sign Out
                  </button>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <main className="relative z-10 max-w-7xl mx-auto px-6">
        {/* Hero */}
        <section className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center py-12 sm:py-20 lg:py-32">
          <motion.div
            className="space-y-8"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="space-y-4">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20"
              >
                <Sparkles className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-white font-medium">AI-Powered YouTube Growth Analytics 2025</span>
              </motion.div>

              <motion.h1
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-gray-100 to-gray-300">
                  Master Your
                </span>
                <br />
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-pink-400 to-purple-400">
                  YouTube Algorithm
                </span>
              </motion.h1>

              <motion.p
                className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-lg"
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                Get AI-powered YouTube analytics that decode the 2025 algorithm. Discover hidden growth opportunities, optimize your content strategy, and accelerate your channel growth with data-driven insights.
              </motion.p>
            </div>

            <motion.div
              className="space-y-4"
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
            >
              <div className="flex flex-col sm:flex-row gap-3 max-w-2xl">
                <div className="relative flex-1">
                  <motion.input
                    className={`w-full bg-white/10 backdrop-blur-sm border ${
                      urlError
                        ? 'border-red-400 shadow-lg shadow-red-400/25'
                        : isUrlFocused
                          ? 'border-pink-400 shadow-lg shadow-pink-400/25'
                          : 'border-white/20'
                    } rounded-2xl px-6 py-4 text-white placeholder-gray-400 focus:outline-none transition-all duration-300`}
                    placeholder="https://www.youtube.com/@yourchannel"
                    value={url}
                    onChange={(e) => {
                      setUrl(e.target.value);
                      if (urlError) setUrlError(null);
                    }}
                    onFocus={() => setIsUrlFocused(true)}
                    onBlur={() => setIsUrlFocused(false)}
                    whileFocus={{ scale: 1.02 }}
                  />
                  <Youtube className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <motion.button
                  onClick={onPreview}
                  className="px-8 py-4 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-2xl font-semibold shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/40 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 sm:min-w-fit"
                  disabled={loading || !url.trim()}
                  whileHover={{ scale: loading ? 1 : 1.05, y: loading ? 0 : -2 }}
                  whileTap={{ scale: loading ? 1 : 0.95 }}
                >
                  {loading ? (
                    <div className="flex items-center justify-center gap-2">
                      <motion.div
                        className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      <span className="hidden sm:inline">Analyzing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <Zap className="w-5 h-5" />
                      <span className="hidden sm:inline">
                        {ctaVariant === "variant_a" ? "Get My Report" :
                         ctaVariant === "variant_b" ? "Analyze Channel" :
                         "Analyze Now"}
                      </span>
                      <span className="sm:hidden">
                        {ctaVariant === "variant_a" ? "Report" :
                         ctaVariant === "variant_b" ? "Analyze" :
                         "Analyze"}
                      </span>
                    </div>
                  )}
                </motion.button>
              </div>

              {/* Quota bar */}
              {quota && (
                <motion.div
                  className="max-w-2xl"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ duration: 1, delay: 1.2 }}
                >
                   {quota && (
                     <div className="flex justify-between text-sm text-gray-400 mb-2">
                       <span>Usage</span>
                       <span>{quota.used}/{quota.limit}</span>
                     </div>
                   )}
                  <div className="w-full h-3 rounded-full bg-white/10 overflow-hidden">
                    <motion.div
                      className="h-3 bg-gradient-to-r from-green-400 to-blue-500 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${usedPct}%` }}
                      transition={{ duration: 1.5, delay: 1.4 }}
                    />
                  </div>
                </motion.div>
              )}
            </motion.div>

            <AnimatePresence>
              {urlError && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl text-red-400 max-w-2xl"
                >
                  {urlError}
                </motion.div>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl text-red-400 max-w-2xl"
                >
                  {error}
                </motion.div>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {preview && (
                <motion.div
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -50, scale: 0.9 }}
                  className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl"
                >
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
                      <Youtube className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-white mb-2">{preview ? String(preview.title ?? "") : ""}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-300 mb-4">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                           {preview ? String(preview.subs ?? "0") : "0"} subscribers
                        </div>
                        {preview && Boolean(preview.lastUpload) && (
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                             {new Date(String(preview.lastUpload)).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                      {user ? (
                        <motion.a
                          href="/checkout"
                          className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all"
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Star className="w-4 h-4" />
                          Get Full Report - Starting at $2
                          <ArrowRight className="w-4 h-4" />
                        </motion.a>
                      ) : (
                        <motion.button
                          onClick={() => signInWithGoogle()}
                          className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:shadow-lg hover:shadow-purple-500/25 transition-all"
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Star className="w-4 h-4" />
                          Sign In to Get Report
                          <ArrowRight className="w-4 h-4" />
                        </motion.button>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Right visuals */}
          <motion.div
            className="hidden lg:block"
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="relative">
              {/* Floating cards */}
              <motion.div
                className="absolute -top-4 -left-4 w-64 h-40 bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                animate={{
                  y: [0, -10, 0],
                  rotate: [-2, 2, -2]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white font-semibold">Growth Insights</span>
                </div>
                <div className="text-sm text-gray-300">
                  +127% avg. view increase
                </div>
              </motion.div>

              <motion.div
                className="absolute top-20 -right-8 w-56 h-36 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur-sm border border-white/20 rounded-2xl p-6"
                animate={{
                  y: [0, 10, 0],
                  rotate: [2, -2, 2]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2
                }}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-400 to-pink-500 rounded-lg flex items-center justify-center">
                    <Target className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white font-semibold">Action Plan</span>
                </div>
                <div className="text-sm text-gray-300">
                  4-week roadmap ready
                </div>
              </motion.div>

              {/* Main feature card */}
              <motion.div
                className="relative z-10 bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 space-y-6"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-semibold text-white">What You Get</span>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {[
                    { icon: BarChart3, text: "AI Growth Strategy", color: "from-blue-400 to-purple-500" },
                    { icon: Play, text: "Viral Content Ideas", color: "from-red-400 to-pink-500" },
                    { icon: Eye, text: "CTR Optimization", color: "from-green-400 to-blue-500" },
                    { icon: Target, text: "Algorithm Insights", color: "from-yellow-400 to-orange-500" },
                    { icon: TrendingUp, text: "Growth Metrics", color: "from-purple-400 to-pink-500" },
                    { icon: Sparkles, text: "Smart Analytics", color: "from-indigo-400 to-purple-500" }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                      whileHover={{ scale: 1.05, y: -2 }}
                    >
                      <div className={`w-8 h-8 bg-gradient-to-r ${item.color} rounded-lg flex items-center justify-center`}>
                        <item.icon className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm text-gray-300 font-medium">{item.text}</span>
                    </motion.div>
                  ))}
                </div>

                <motion.div
                  className="pt-4 border-t border-white/10"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 2 }}
                >
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Analysis time</span>
                    <span className="text-white font-semibold">~2 minutes</span>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </section>

        {/* How it works */}
        <section id="how" className="py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              From Analysis to <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-400">Growth</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Transform your YouTube channel with AI-powered insights that actually work
            </p>
          </motion.div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Connect Your Channel",
                description: "Simply paste your YouTube channel URL and let our AI do the heavy lifting. We analyze your content performance instantly.",
                icon: Youtube,
                color: "from-red-400 to-pink-500",
                delay: 0.2
              },
              {
                step: "02",
                title: "AI-Powered Insights",
                description: "Our advanced AI discovers what makes your best videos successful and identifies untapped growth opportunities.",
                icon: Sparkles,
                color: "from-purple-400 to-pink-500",
                delay: 0.4
              },
              {
                step: "03",
                title: "Get Your Growth Plan",
                description: "Receive comprehensive SWOT analysis, algorithm optimization tips, content strategy, thumbnail analysis, and a strategic growth roadmap.",
                icon: Zap,
                color: "from-yellow-400 to-orange-500",
                delay: 0.6
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: item.delay }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 hover:bg-white/15 transition-all duration-300 h-full">
                  <div className="flex items-center gap-4 mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${item.color} rounded-2xl flex items-center justify-center`}>
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-6xl font-bold text-white/10">{item.step}</div>
                  </div>

                  <h3 className="text-2xl font-bold text-white mb-4">{item.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{item.description}</p>

                  <motion.div
                    className="mt-6 w-full h-1 bg-white/10 rounded-full overflow-hidden"
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    transition={{ duration: 1, delay: item.delay + 0.5 }}
                    viewport={{ once: true }}
                  >
                    <div className={`h-full bg-gradient-to-r ${item.color} rounded-full`} />
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Pricing */}
        <section id="pricing" className="py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Simple <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-blue-400">Pricing</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Choose the perfect plan for your YouTube growth journey
            </p>
          </motion.div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                name: "First Timer",
                price: "$2.00",
                originalPrice: "$4.99",
                description: "Perfect for trying out our service",
                features: [
                  "✅ AI-Powered Analytics Dashboard",
                  "✅ 2025 Algorithm Insights",
                  "✅ Viral Content Recommendations",
                  "✅ Growth Strategy Roadmap",
                  "✅ Smart Export & Reports"
                ],
                popular: false,
                color: "from-blue-400 to-purple-500",
                delay: 0.1
              },
              {
                name: "Single Report",
                price: "$4.99",
                description: "One comprehensive analysis",
                features: [
                  "✅ AI-Powered Analytics Dashboard",
                  "✅ 2025 Algorithm Insights",
                  "✅ Viral Content Recommendations",
                  "✅ Growth Strategy Roadmap",
                  "✅ Smart Export & Reports"
                ],
                popular: false,
                color: "from-purple-400 to-pink-500",
                delay: 0.2
              },
              {
                name: "Creator Pack",
                price: "$19.99",
                description: "5 reports + memory system",
                features: [
                  "✅ Everything in Single Report",
                  "🧠 Memory System*",
                  "📈 Progress tracking over time",
                  "🎯 Trend analysis across reports",
                  "⚡ Priority Support"
                ],
                popular: true,
                color: "from-pink-400 to-red-500",
                delay: 0.3
              },
              {
                name: "Pro Pack",
                price: "$34.99",
                description: "10 reports + advanced features",
                features: [
                  "✅ Everything in Creator Pack",
                  "🧠 Advanced Memory System*",
                  "📊 Multi-channel comparison",
                  "🔮 Predictive growth modeling",
                  "🏆 Competitive benchmarking",
                  "💎 Premium Support"
                ],
                popular: false,
                color: "from-green-400 to-blue-500",
                delay: 0.4
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: plan.delay }}
                viewport={{ once: true }}
                className="relative group"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-pink-500 to-red-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-r ${plan.color} rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-all duration-300`} />
                <div className={`relative bg-white/10 backdrop-blur-sm border ${plan.popular ? 'border-pink-400/50' : 'border-white/20'} rounded-3xl p-8 hover:bg-white/15 transition-all duration-300 h-full`}>
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                    <div className="mb-2">
                      <div className="text-5xl font-bold text-white">{plan.price}</div>
                      {'originalPrice' in plan && (
                        <div className="text-lg text-gray-400 line-through">{(plan as { originalPrice: string }).originalPrice}</div>
                      )}
                    </div>
                    <p className="text-gray-300 text-sm">{plan.description}</p>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3 text-gray-300">
                        <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <motion.a
                    href="/checkout"
                    className={`block w-full text-center py-4 rounded-2xl font-semibold transition-all ${
                      plan.popular
                        ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-lg shadow-pink-500/25 hover:shadow-xl hover:shadow-pink-500/40'
                        : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                    }`}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Get Started
                  </motion.a>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Memory System Explanation */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-12 text-center"
          >
            <p className="text-gray-400 text-sm">
              <span className="text-yellow-400">*</span> Memory System: Compares your new reports with previous ones to track improvement rates and provide even better recommendations based on your growth patterns.
            </p>
          </motion.div>
        </section>

        {/* Features showcase */}
        <section id="features" className="py-20">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Why Choose <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">Ytuber</span>
            </h2>
          </motion.div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
            {[
              {
                icon: Target,
                title: "AI Growth Intelligence",
                description: "Master the 2025 YouTube algorithm with AI-powered insights and personalized growth strategies",
                color: "from-green-400 to-blue-500"
              },
              {
                icon: Zap,
                title: "Real-Time Analytics",
                description: "Get instant access to your channel's performance data with AI-enhanced insights and trend analysis",
                color: "from-yellow-400 to-orange-500"
              },
              {
                icon: BarChart3,
                title: "Viral Content Engine",
                description: "Discover trending topics and optimize your content for maximum reach with our AI-powered recommendations",
                color: "from-purple-400 to-pink-500"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className={`w-20 h-20 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Footer */}
        <footer className="border-t border-white/10 py-12">
          <div className="max-w-6xl mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-8"
            >
              <div className="flex items-center justify-center gap-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Youtube className="w-5 h-5 text-white" />
                </div>
                <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  ytuber
                </span>
              </div>
              <p className="text-gray-300 max-w-2xl mx-auto leading-relaxed">
                Ytuber helps creators grow responsibly and efficiently. We keep API usage within quota,
                avoid wasteful search.list calls, and generate prescriptive plans backed by your recent performance data.
              </p>
            </motion.div>

            {/* Footer Links */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex flex-wrap justify-center gap-6 mb-6 text-sm"
            >
              <a href="/about" className="text-gray-400 hover:text-white transition-colors">
                About Us
              </a>
              <Link href="/blog" className="text-gray-400 hover:text-white transition-colors">
                YouTube Growth Blog
              </Link>
              <a href="/youtube-growth" className="text-gray-400 hover:text-white transition-colors">
                YouTube Growth
              </a>
              <a href="/youtube-analytics" className="text-gray-400 hover:text-white transition-colors">
                YouTube Analytics
              </a>
              <a href="/youtube-growth-tools" className="text-gray-400 hover:text-white transition-colors">
                Growth Tools
              </a>
              <a href="/youtube-growth-tips" className="text-gray-400 hover:text-white transition-colors">
                Growth Tips
              </a>
              <a href="/contact" className="text-gray-400 hover:text-white transition-colors">
                Contact
              </a>
              <a href="/faq" className="text-gray-400 hover:text-white transition-colors">
                FAQ
              </a>
              <a href="/testimonials" className="text-gray-400 hover:text-white transition-colors">
                Testimonials
              </a>
              <a href="/success-stories" className="text-gray-400 hover:text-white transition-colors">
                Success Stories
              </a>
              <a href="/reviews" className="text-gray-400 hover:text-white transition-colors">
                Reviews
              </a>
              <a href="/shipping-returns" className="text-gray-400 hover:text-white transition-colors">
                Shipping & Returns Policy
              </a>
              <a href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms of Sale
              </a>
            </motion.div>

            {/* Payment Methods */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex justify-center mb-6"
            >
              <div className="bg-white/50 backdrop-blur-sm rounded-lg p-3">
                <Image
                  src="/assets/payment.png"
                  alt="Accepted payment methods: Iyzico, Mastercard, Visa, American Express, Troy"
                  width={446}
                  height={40}
                  className="h-8 opacity-90"
                />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="text-center text-sm text-gray-400"
            >
              <p>&copy; 2025 Ytuber. Built with ❤️ for YouTube creators.</p>
            </motion.div>
          </div>
        </footer>
      </main>
      </div>
    </>
  );
}
