import { Metadata } from 'next';
import Link from 'next/link';
import { Zap, Target, BarChart3, TrendingUp, Users, Eye, ArrowRight, CheckCircle, Star } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Growth Tools | Professional Creator Tools - YTuber',
  description: 'Discover the best YouTube growth tools for content creators. Analytics, optimization, and growth tracking tools to scale your YouTube channel.',
  keywords: 'YouTube growth tools, YouTube creator tools, YouTube optimization tools, YouTube analytics tools, YouTube growth software, best YouTube tools',
};

const growthTools = [
  {
    icon: BarChart3,
    title: "Advanced Analytics Dashboard",
    description: "Comprehensive analytics that go beyond YouTube Studio with detailed insights and growth recommendations.",
    features: ["Real-time performance tracking", "Competitor analysis", "Growth trend forecasting", "Custom reporting"],
    category: "Analytics"
  },
  {
    icon: Target,
    title: "Algorithm Optimization",
    description: "Understand and optimize for YouTube's algorithm to increase your video visibility and reach.",
    features: ["Algorithm compatibility scoring", "SEO optimization tips", "Trending topic analysis", "Best posting times"],
    category: "Optimization"
  },
  {
    icon: TrendingUp,
    title: "Growth Tracking",
    description: "Monitor your channel's growth with detailed metrics and milestone tracking across all key performance indicators.",
    features: ["Subscriber growth tracking", "View velocity analysis", "Engagement rate monitoring", "Revenue tracking"],
    category: "Growth"
  },
  {
    icon: Users,
    title: "Audience Insights",
    description: "Deep dive into your audience demographics, behavior patterns, and content preferences.",
    features: ["Demographic analysis", "Viewing behavior insights", "Content preference mapping", "Audience retention analysis"],
    category: "Audience"
  },
  {
    icon: Eye,
    title: "Content Optimization",
    description: "Optimize your titles, thumbnails, and descriptions for maximum click-through rates and engagement.",
    features: ["Title optimization suggestions", "Thumbnail performance analysis", "Description SEO tips", "Tag recommendations"],
    category: "Content"
  },
  {
    icon: Zap,
    title: "Performance Alerts",
    description: "Get notified about important changes in your channel performance and growth opportunities.",
    features: ["Real-time performance alerts", "Growth opportunity notifications", "Trend alerts", "Milestone celebrations"],
    category: "Monitoring"
  }
];

const toolCategories = [
  {
    name: "Analytics Tools",
    description: "Track and analyze your YouTube performance",
    tools: ["Performance Dashboard", "Audience Analytics", "Revenue Tracking", "Competitor Analysis"]
  },
  {
    name: "Optimization Tools", 
    description: "Optimize your content for better performance",
    tools: ["SEO Optimization", "Thumbnail Analysis", "Title Suggestions", "Tag Recommendations"]
  },
  {
    name: "Growth Tools",
    description: "Accelerate your channel growth",
    tools: ["Growth Tracking", "Trend Analysis", "Algorithm Insights", "Best Practices"]
  }
];

const pricingPlans = [
  {
    name: "First Timer",
    price: "$2.00",
    description: "Perfect for trying our platform",
    features: ["1 comprehensive report", "Basic analytics", "Growth recommendations", "PDF export"],
    popular: false
  },
  {
    name: "Creator Pack",
    price: "$19.99",
    description: "Best for growing creators",
    features: ["5 detailed reports", "Memory system", "Advanced analytics", "Priority support", "JSON export"],
    popular: true
  },
  {
    name: "Pro Pack",
    price: "$34.99",
    description: "For serious content creators",
    features: ["10 comprehensive reports", "Advanced memory system", "Premium analytics", "Priority support", "All export formats"],
    popular: false
  }
];

export default function YouTubeGrowthToolsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'YouTube Growth Tools', href: '/youtube-growth-tools' }]}
            className="mb-8"
          />

          {/* Hero Section */}
          <div className="text-center mb-20">
            <h1 className="text-6xl font-bold text-white mb-6">
              YouTube Growth
              <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"> Tools</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Professional tools designed to help YouTube creators analyze, optimize, and grow their channels. 
              Everything you need to succeed on YouTube in one comprehensive platform.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <Zap className="w-5 h-5 mr-2" />
              Try Our Tools
            </Link>
          </div>

          {/* Growth Tools Grid */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Complete YouTube Growth Toolkit
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                All the tools you need to analyze, optimize, and grow your YouTube channel
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {growthTools.map((tool, index) => {
                const IconComponent = tool.icon;
                return (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10 hover:bg-white/15 transition-all duration-300">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-white">{tool.title}</h3>
                        <span className="text-xs text-blue-400 font-medium">{tool.category}</span>
                      </div>
                    </div>
                    
                    <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                      {tool.description}
                    </p>
                    
                    <ul className="space-y-1">
                      {tool.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2 text-gray-300 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Tool Categories */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Tool Categories
              </h2>
              <p className="text-xl text-gray-300">
                Organized tools for every aspect of YouTube growth
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {toolCategories.map((category, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                  <h3 className="text-xl font-bold text-white mb-3">{category.name}</h3>
                  <p className="text-gray-300 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.tools.map((tool, toolIndex) => (
                      <li key={toolIndex} className="flex items-center gap-2 text-gray-300 text-sm">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        {tool}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Pricing Section */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Choose Your Growth Plan
              </h2>
              <p className="text-xl text-gray-300">
                Affordable pricing for creators at every stage
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {pricingPlans.map((plan, index) => (
                <div key={index} className={`bg-white/10 backdrop-blur-lg rounded-xl p-6 border ${plan.popular ? 'border-blue-500 ring-2 ring-blue-500/20' : 'border-white/10'} relative`}>
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                        Most Popular
                      </span>
                    </div>
                  )}
                  
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                    <div className="text-3xl font-bold text-white mb-2">{plan.price}</div>
                    <p className="text-gray-300 text-sm">{plan.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3 text-gray-300 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Link
                    href="/"
                    className={`block w-full text-center py-3 rounded-lg font-medium transition-all duration-300 ${
                      plan.popular 
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700' 
                        : 'bg-white/10 text-white hover:bg-white/20'
                    }`}
                  >
                    Get Started
                  </Link>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Start Using Professional YouTube Tools Today
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Join thousands of creators who are growing their YouTube channels with our comprehensive toolkit. 
                Get started with detailed analytics and growth insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Try Tools Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/blog"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
