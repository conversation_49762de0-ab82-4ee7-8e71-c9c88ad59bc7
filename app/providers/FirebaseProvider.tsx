"use client";
import { ReactNode, useEffect } from "react";

export default function FirebaseProvider({ children }: { children: ReactNode }) {
  useEffect(() => {
    if (typeof window === "undefined") return;
    (async () => {
      try {
        const { initializeApp, getApps, getApp } = await import("firebase/app");
        const firebaseConfig = {
          apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
          authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
          projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
          storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
          appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
          measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
        } as const;
        if (!getApps().length) initializeApp(firebaseConfig);

        const { getAnalytics, isSupported } = await import("firebase/analytics");
        if (await isSupported()) {
          getAnalytics(getApp());
        }
      } catch {
        // no-op
      }
    })();
  }, []);
  return <>{children}</>;
}


