"use client";

import { createContext, useContext, useEffect, useRef, useState, ReactNode } from "react";
import { User } from "firebase/auth";
import { onAuthStateChange } from "@/lib/auth";

interface AuthContextType {
  user: User | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const triedCustomToken = useRef(false);

  // Keep Firebase Auth state in sync
  useEffect(() => {
    const unsubscribe = onAuthStateChange((u) => {
      setUser(u);
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  // Fallback: if we have a secure session cookie but no Firebase client user yet,
  // try to exchange it for a Firebase custom token and sign in automatically.
  useEffect(() => {
    if (user || triedCustomToken.current) return;
    triedCustomToken.current = true;

    (async () => {
      try {
        setLoading(true);
        const res = await fetch("/api/auth/firebase/token", { credentials: "include" });
        if (!res.ok) return; // No session cookie or server couldn't create token
        const { customToken } = await res.json();
        if (!customToken) return;

        const { getAuth, signInWithCustomToken } = await import("firebase/auth");
        const { auth } = await import("@/lib/firebase/client");
        await signInWithCustomToken(auth as unknown as ReturnType<typeof getAuth>, customToken);
      } catch {
        // Ignore – user will stay signed out
      } finally {
        setLoading(false);
      }
    })();
  }, [user]);

  return (
    <AuthContext.Provider value={{ user, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
