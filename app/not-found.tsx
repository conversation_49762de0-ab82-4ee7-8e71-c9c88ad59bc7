import Link from 'next/link';
import { Home, Search, TrendingUp, ArrowR<PERSON>, Youtube } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animated 404 */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-transparent bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text mb-4">
            404
          </div>
          <div className="flex items-center justify-center gap-2 mb-6">
            <Youtube className="w-8 h-8 text-red-500 animate-pulse" />
            <span className="text-2xl text-white font-semibold">Page Not Found</span>
          </div>
        </div>

        {/* Error Message */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10 mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">
            Oops! This page got lost in the algorithm
          </h1>
          <p className="text-gray-300 text-lg leading-relaxed mb-6">
            Just like a video that doesn&apos;t get recommended, this page seems to have disappeared.
            But don&apos;t worry - we&apos;ll help you find what you&apos;re looking for!
          </p>
          
          {/* Quick Actions */}
          <div className="grid md:grid-cols-3 gap-4 mb-8">
            <Link 
              href="/"
              className="flex items-center gap-3 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors border border-white/10"
            >
              <Home className="w-5 h-5 text-blue-400" />
              <span className="text-white font-medium">Go Home</span>
            </Link>
            
            <Link 
              href="/blog"
              className="flex items-center gap-3 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors border border-white/10"
            >
              <Search className="w-5 h-5 text-green-400" />
              <span className="text-white font-medium">Browse Blog</span>
            </Link>
            
            <Link 
              href="/youtube-growth"
              className="flex items-center gap-3 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors border border-white/10"
            >
              <TrendingUp className="w-5 h-5 text-purple-400" />
              <span className="text-white font-medium">Growth Tips</span>
            </Link>
          </div>
        </div>

        {/* Popular Pages */}
        <div className="text-left">
          <h2 className="text-xl font-bold text-white mb-4">Popular Pages</h2>
          <div className="space-y-3">
            {[
              { title: "YouTube Growth Strategies", href: "/youtube-growth", desc: "Learn proven strategies to grow your channel" },
              { title: "YouTube Analytics Guide", href: "/youtube-analytics", desc: "Master your YouTube analytics dashboard" },
              { title: "Growth Tips & Tricks", href: "/youtube-growth-tips", desc: "Quick wins for immediate channel growth" },
              { title: "FAQ", href: "/faq", desc: "Common questions about YouTube growth" }
            ].map((page, index) => (
              <Link 
                key={index}
                href={page.href}
                className="block p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-colors border border-white/10 group"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-white font-medium group-hover:text-blue-400 transition-colors">
                      {page.title}
                    </h3>
                    <p className="text-gray-400 text-sm">{page.desc}</p>
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-blue-400 group-hover:translate-x-1 transition-all" />
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="mt-12">
          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <Home className="w-5 h-5 mr-2" />
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}
