import { Metadata } from 'next';
import Link from 'next/link';
import { Star, ThumbsUp, MessageCircle, ArrowRight } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YTuber Reviews & Ratings | What Creators Say About Our Analytics - YTuber',
  description: 'Read honest reviews from YouTube creators about YTuber analytics platform. See ratings, feedback, and experiences from real users.',
  keywords: 'YTuber reviews, YouTube analytics reviews, creator feedback, user ratings, YouTube growth tool reviews',
  openGraph: {
    title: 'YTuber Reviews & Ratings | What Creators Say About Our Analytics',
    description: 'Read honest reviews from YouTube creators about YTuber analytics platform.',
    url: 'https://ytuber.life/reviews',
    type: 'website',
  },
  other: {
    canonical: 'https://ytuber.life/reviews',
  },
};

const reviewStats = {
  averageRating: 4.8,
  totalReviews: 12847,
  ratingDistribution: [
    { stars: 5, count: 9876, percentage: 77 },
    { stars: 4, count: 2314, percentage: 18 },
    { stars: 3, count: 385, percentage: 3 },
    { stars: 2, count: 154, percentage: 1 },
    { stars: 1, count: 118, percentage: 1 }
  ]
};

const reviews = [
  {
    id: 1,
    name: "<PERSON>",
    channel: "Tech Insights Daily",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    date: "2025-01-15",
    verified: true,
    helpful: 234,
    review: "YTuber completely transformed my channel strategy. The algorithm insights are incredibly accurate - I went from 50K to 500K subscribers in just 6 months by following their recommendations. The thumbnail analysis feature alone is worth the price!",
    pros: ["Accurate algorithm insights", "Easy to understand interface", "Great customer support"],
    cons: ["Could use more export options"],
    category: "Technology",
    featured: true
  },
  {
    id: 2,
    name: "Sarah Martinez",
    channel: "Cooking with Love",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    date: "2025-01-12",
    verified: true,
    helpful: 189,
    review: "As a food creator, understanding my audience was crucial. YTuber's analytics showed me exactly when my viewers were most active and what content they craved. My engagement rate doubled in 3 months!",
    pros: ["Detailed audience insights", "Content recommendations", "Trend analysis"],
    cons: ["Learning curve for beginners"],
    category: "Food & Cooking",
    featured: true
  },
  {
    id: 3,
    name: "Mike Johnson",
    channel: "Fitness Revolution",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    rating: 4,
    date: "2025-01-10",
    verified: true,
    helpful: 156,
    review: "Great tool for understanding YouTube analytics. The competitor analysis feature helped me find gaps in my content strategy. Only wish it had more social media integration.",
    pros: ["Competitor analysis", "Growth tracking", "Regular updates"],
    cons: ["Limited social media features", "Price could be lower"],
    category: "Fitness",
    featured: false
  },
  {
    id: 4,
    name: "Emma Thompson",
    channel: "Creative DIY",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    date: "2025-01-08",
    verified: true,
    helpful: 203,
    review: "YTuber's content gap analysis revealed opportunities I never knew existed. I created videos for underserved topics and saw immediate growth. The ROI has been incredible!",
    pros: ["Content gap analysis", "ROI tracking", "Actionable insights"],
    cons: ["None so far"],
    category: "DIY & Crafts",
    featured: false
  },
  {
    id: 5,
    name: "David Kim",
    channel: "Business Mastery",
    avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    date: "2025-01-05",
    verified: true,
    helpful: 178,
    review: "The memory system is a game-changer. Being able to track my progress over time and see how changes impact my channel has been invaluable. Highly recommend for serious creators.",
    pros: ["Memory system", "Progress tracking", "Historical data"],
    cons: ["Could use mobile app"],
    category: "Business",
    featured: false
  },
  {
    id: 6,
    name: "Lisa Wang",
    channel: "Travel Adventures",
    avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
    rating: 4,
    date: "2025-01-03",
    verified: true,
    helpful: 142,
    review: "Solid analytics platform with great insights. The upload timing recommendations helped optimize my posting schedule. Would love to see more international market analysis.",
    pros: ["Upload timing insights", "Clean interface", "Reliable data"],
    cons: ["Limited international features", "Could use more customization"],
    category: "Travel",
    featured: false
  }
];

const categories = ["All", "Technology", "Food & Cooking", "Fitness", "DIY & Crafts", "Business", "Travel"];

export default function ReviewsPage() {
  const featuredReviews = reviews.filter(review => review.featured);
  const regularReviews = reviews.filter(review => !review.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'Reviews', href: '/reviews' }]}
            className="mb-8"
          />

          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-white">
                Creator Reviews & Ratings
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              See what YouTube creators are saying about YTuber analytics. 
              Real reviews from real creators who transformed their channels in 2025.
            </p>
          </div>

          {/* Review Stats */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10 mb-12">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Overall Rating */}
              <div className="text-center">
                <div className="text-6xl font-bold text-white mb-2">{reviewStats.averageRating}</div>
                <div className="flex items-center justify-center gap-1 mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`w-6 h-6 ${i < Math.floor(reviewStats.averageRating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}`} 
                    />
                  ))}
                </div>
                <div className="text-gray-300">Based on {reviewStats.totalReviews.toLocaleString()} reviews</div>
              </div>

              {/* Rating Distribution */}
              <div className="space-y-2">
                {reviewStats.ratingDistribution.map((rating) => (
                  <div key={rating.stars} className="flex items-center gap-3">
                    <div className="flex items-center gap-1 w-12">
                      <span className="text-white text-sm">{rating.stars}</span>
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    </div>
                    <div className="flex-1 bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-yellow-400 h-2 rounded-full" 
                        style={{ width: `${rating.percentage}%` }}
                      />
                    </div>
                    <div className="text-gray-300 text-sm w-12">{rating.percentage}%</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-8 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors border border-white/10"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Featured Reviews */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">Featured Reviews</h2>
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredReviews.map((review) => (
                <ReviewCard key={review.id} review={review} featured={true} />
              ))}
            </div>
          </div>

          {/* All Reviews */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">All Reviews</h2>
            <div className="space-y-6">
              {regularReviews.map((review) => (
                <ReviewCard key={review.id} review={review} featured={false} />
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Join Thousands of Satisfied Creators
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                See why creators love YTuber analytics. Start your growth journey today 
                and become our next success story.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Try YTuber Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/testimonials"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Read More Stories
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ReviewType {
  id: number;
  name: string;
  channel: string;
  avatar: string;
  rating: number;
  date: string;
  verified: boolean;
  helpful: number;
  review: string;
  pros: string[];
  cons: string[];
  category: string;
  featured: boolean;
}

function ReviewCard({ review, featured }: { review: ReviewType; featured: boolean }) {
  return (
    <div className={`bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10 ${featured ? 'lg:p-8' : ''}`}>
      {/* Header */}
      <div className="flex items-start gap-4 mb-4">
        <img 
          src={review.avatar} 
          alt={review.name}
          className="w-12 h-12 rounded-full object-cover border-2 border-white/20"
        />
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-bold text-white">{review.name}</h3>
            {review.verified && (
              <div className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                Verified
              </div>
            )}
          </div>
          <div className="text-sm text-gray-400 mb-2">{review.channel}</div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star 
                  key={i} 
                  className={`w-4 h-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`} 
                />
              ))}
            </div>
            <span className="text-sm text-gray-400">{review.date}</span>
          </div>
        </div>
        <div className="text-right">
          <span className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">
            {review.category}
          </span>
        </div>
      </div>

      {/* Review Content */}
      <p className="text-gray-300 leading-relaxed mb-4">
        {review.review}
      </p>

      {/* Pros & Cons */}
      {featured && (
        <div className="grid md:grid-cols-2 gap-4 mb-4">
          <div>
            <h4 className="text-sm font-semibold text-green-400 mb-2">Pros:</h4>
            <ul className="space-y-1">
              {review.pros.map((pro: string, index: number) => (
                <li key={index} className="text-gray-300 text-sm flex items-start gap-2">
                  <div className="w-1 h-1 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                  {pro}
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-semibold text-red-400 mb-2">Cons:</h4>
            <ul className="space-y-1">
              {review.cons.map((con: string, index: number) => (
                <li key={index} className="text-gray-300 text-sm flex items-start gap-2">
                  <div className="w-1 h-1 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                  {con}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-white/10">
        <button className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors">
          <ThumbsUp className="w-4 h-4" />
          <span className="text-sm">Helpful ({review.helpful})</span>
        </button>
        <button className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors">
          <MessageCircle className="w-4 h-4" />
          <span className="text-sm">Reply</span>
        </button>
      </div>
    </div>
  );
}
