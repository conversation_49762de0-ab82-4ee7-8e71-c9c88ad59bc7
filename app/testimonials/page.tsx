import { Metadata } from 'next';
import Link from 'next/link';
import { Star, Play, TrendingUp, Users, BarChart3, <PERSON>uote, ArrowRight, Youtube } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Creator Success Stories & Testimonials - YTuber',
  description: 'Read real success stories from YouTube creators who grew their channels using YTuber analytics. See how our insights helped them achieve their goals.',
  keywords: 'YouTube success stories, creator testimonials, YouTube growth results, channel growth case studies, YouTube analytics success',
  openGraph: {
    title: 'YouTube Creator Success Stories & Testimonials - YTuber',
    description: 'Read real success stories from YouTube creators who grew their channels using YTuber analytics.',
    url: 'https://ytuber.life/testimonials',
    type: 'website',
  },
  other: {
    canonical: 'https://ytuber.life/testimonials',
  },
};

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    channel: "Tech Simplified",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    subscribers: "2.3M",
    growth: "+847% in 6 months",
    category: "Technology",
    quote: "YTuber's analytics showed me exactly which content my audience craved. The algorithm insights were game-changing - I went from 50K to 2.3M subscribers by following their recommendations.",
    results: [
      { metric: "Subscribers", before: "50K", after: "2.3M", growth: "+4,500%" },
      { metric: "Monthly Views", before: "200K", after: "15M", growth: "+7,400%" },
      { metric: "Revenue", before: "$500", after: "$45K", growth: "+8,900%" }
    ],
    videoTestimonial: true,
    featured: true
  },
  {
    id: 2,
    name: "Marcus Rodriguez",
    channel: "Fitness Revolution",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    subscribers: "890K",
    growth: "+312% in 4 months",
    category: "Fitness & Health",
    quote: "The thumbnail analysis feature was incredible. YTuber showed me why my CTR was low and how to fix it. My views exploded overnight!",
    results: [
      { metric: "Click-Through Rate", before: "2.1%", after: "8.7%", growth: "+314%" },
      { metric: "Watch Time", before: "45%", after: "73%", growth: "+62%" },
      { metric: "Engagement", before: "1.2%", after: "4.8%", growth: "+300%" }
    ],
    videoTestimonial: false,
    featured: true
  },
  {
    id: 3,
    name: "Emma Thompson",
    channel: "Creative Cooking",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    subscribers: "1.5M",
    growth: "+523% in 8 months",
    category: "Food & Cooking",
    quote: "YTuber's content strategy insights helped me understand what my audience actually wanted to see. My cooking tutorials now get 10x more engagement!",
    results: [
      { metric: "Average Views", before: "5K", after: "250K", growth: "+4,900%" },
      { metric: "Comments", before: "50", after: "2.1K", growth: "+4,100%" },
      { metric: "Shares", before: "10", after: "890", growth: "+8,800%" }
    ],
    videoTestimonial: true,
    featured: false
  },
  {
    id: 4,
    name: "David Kim",
    channel: "Startup Stories",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    subscribers: "650K",
    growth: "+289% in 5 months",
    category: "Business",
    quote: "The competitor analysis feature opened my eyes to gaps in my content strategy. YTuber helped me find my unique angle in a crowded niche.",
    results: [
      { metric: "Subscriber Growth", before: "100/month", after: "15K/month", growth: "+14,900%" },
      { metric: "Brand Deals", before: "0", after: "12", growth: "∞" },
      { metric: "Monthly Revenue", before: "$0", after: "$28K", growth: "∞" }
    ],
    videoTestimonial: false,
    featured: false
  },
  {
    id: 5,
    name: "Zoe Williams",
    channel: "Mindful Living",
    avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    subscribers: "420K",
    growth: "+178% in 3 months",
    category: "Lifestyle",
    quote: "YTuber's audience insights revealed that my viewers wanted shorter, more actionable content. This simple change doubled my retention rate!",
    results: [
      { metric: "Retention Rate", before: "35%", after: "68%", growth: "+94%" },
      { metric: "Session Duration", before: "3.2 min", after: "7.8 min", growth: "+144%" },
      { metric: "Playlist Adds", before: "20", after: "450", growth: "+2,150%" }
    ],
    videoTestimonial: true,
    featured: false
  },
  {
    id: 6,
    name: "Alex Johnson",
    channel: "Gaming Insights",
    avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face",
    subscribers: "1.8M",
    growth: "+445% in 7 months",
    category: "Gaming",
    quote: "The trending topics feature helped me stay ahead of gaming trends. I was covering viral games before they exploded, leading to massive growth!",
    results: [
      { metric: "Viral Videos", before: "0", after: "8", growth: "∞" },
      { metric: "Peak Concurrent", before: "500", after: "45K", growth: "+8,900%" },
      { metric: "Sponsorship Value", before: "$200", after: "$15K", growth: "+7,400%" }
    ],
    videoTestimonial: false,
    featured: false
  }
];

const stats = [
  { label: "Creators Helped", value: "50K+", icon: Users },
  { label: "Total Growth", value: "2.3B+", icon: TrendingUp, suffix: "views" },
  { label: "Success Rate", value: "94%", icon: BarChart3 },
  { label: "Avg Growth", value: "347%", icon: Star }
];

export default function TestimonialsPage() {
  const featuredTestimonials = testimonials.filter(t => t.featured);
  const regularTestimonials = testimonials.filter(t => !t.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'Success Stories', href: '/testimonials' }]}
            className="mb-8"
          />

          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-white">
                Creator Success Stories
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Real YouTube creators sharing their incredible growth journeys with YTuber. 
              See how our analytics and insights transformed their channels in 2025.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                    <div className="flex items-center justify-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">
                      {stat.value}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {stat.label} {stat.suffix && <span className="text-xs">({stat.suffix})</span>}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Featured Success Stories */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">Featured Success Stories</h2>
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredTestimonials.map((testimonial) => (
                <TestimonialCard key={testimonial.id} testimonial={testimonial} featured={true} />
              ))}
            </div>
          </div>

          {/* All Testimonials */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">More Success Stories</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularTestimonials.map((testimonial) => (
                <TestimonialCard key={testimonial.id} testimonial={testimonial} featured={false} />
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Write Your Success Story?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Join thousands of creators who transformed their YouTube channels with our analytics. 
                Your growth story could be next!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Start Your Growth Journey
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/youtube-growth"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Learn Growth Strategies
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface TestimonialType {
  id: number;
  name: string;
  channel: string;
  avatar: string;
  subscribers: string;
  growth: string;
  category: string;
  quote: string;
  results: Array<{ metric: string; before: string; after: string; growth: string }>;
  videoTestimonial: boolean;
  featured: boolean;
}

function TestimonialCard({ testimonial, featured }: { testimonial: TestimonialType; featured: boolean }) {
  return (
    <div className={`bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/10 ${featured ? 'lg:p-8' : ''}`}>
      {/* Header */}
      <div className="flex items-start gap-4 mb-6">
        <img 
          src={testimonial.avatar} 
          alt={testimonial.name}
          className="w-16 h-16 rounded-full object-cover border-2 border-white/20"
        />
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-bold text-white">{testimonial.name}</h3>
            {testimonial.videoTestimonial && (
              <div className="flex items-center gap-1 px-2 py-1 bg-red-500/20 rounded-full">
                <Play className="w-3 h-3 text-red-400" />
                <span className="text-xs text-red-400">Video</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Youtube className="w-4 h-4" />
            <span>{testimonial.channel}</span>
          </div>
          <div className="flex items-center gap-4 mt-2">
            <span className="text-sm text-blue-400">{testimonial.subscribers} subscribers</span>
            <span className="text-sm text-green-400">{testimonial.growth}</span>
          </div>
        </div>
      </div>

      {/* Quote */}
      <div className="relative mb-6">
        <Quote className="w-6 h-6 text-blue-400 mb-3" />
        <p className="text-gray-300 leading-relaxed italic">
          &quot;{testimonial.quote}&quot;
        </p>
      </div>

      {/* Results */}
      {featured && (
        <div className="space-y-3">
          <h4 className="text-sm font-semibold text-white mb-3">Growth Results:</h4>
          {testimonial.results.map((result, index: number) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <span className="text-gray-300 text-sm">{result.metric}</span>
              <div className="text-right">
                <div className="text-white font-medium text-sm">
                  {result.before} → {result.after}
                </div>
                <div className="text-green-400 text-xs">{result.growth}</div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Category Badge */}
      <div className="mt-4 pt-4 border-t border-white/10">
        <span className="inline-block px-3 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
          {testimonial.category}
        </span>
      </div>
    </div>
  );
}
