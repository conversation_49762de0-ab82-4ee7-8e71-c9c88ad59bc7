import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { HelpCircle, TrendingUp, BarChart3, Users, Zap } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { faqSchema } from '@/lib/seo/schemas';
import FAQClient from './FAQClient';

export const metadata: Metadata = {
  title: 'YouTube Growth FAQ | Common Questions About Growing Your Channel - YTuber',
  description: 'Get answers to frequently asked questions about YouTube growth, analytics, and channel optimization. Learn how to grow your YouTube channel effectively.',
  keywords: 'YouTube growth FAQ, how to grow YouTube channel, YouTube analytics questions, YouTube growth tips, YouTube channel growth help',
};

const faqCategories = [
  {
    icon: TrendingUp,
    title: "YouTube Growth",
    color: "from-green-400 to-blue-500",
    faqs: [
      {
        question: "How long does it take to grow a YouTube channel?",
        answer: "YouTube growth timelines vary significantly based on your niche, content quality, and consistency. Most creators see initial growth within 3-6 months of consistent posting, but substantial growth typically takes 6-12 months. With proper analytics and strategy implementation, you can accelerate this timeline."
      },
      {
        question: "What are the best YouTube growth strategies for 2025?",
        answer: "The most effective YouTube growth strategies in 2025 include: optimizing for YouTube Shorts, focusing on audience retention, creating compelling thumbnails, using data-driven content planning, engaging with your community, collaborating with other creators, and leveraging YouTube Analytics to understand what works."
      },
      {
        question: "How many videos should I upload per week to grow my channel?",
        answer: "Quality over quantity is key. It's better to upload 1-2 high-quality videos per week consistently than to upload daily with lower quality. Focus on maintaining a schedule you can sustain long-term while ensuring each video provides value to your audience."
      },
      {
        question: "How do I get my first 1000 subscribers on YouTube?",
        answer: "To reach your first 1000 subscribers: create content around trending topics in your niche, optimize your titles and thumbnails for clicks, engage with your audience in comments, collaborate with other small creators, promote your videos on social media, and use YouTube Analytics to understand what content performs best."
      }
    ]
  },
  {
    icon: BarChart3,
    title: "YouTube Analytics",
    color: "from-purple-400 to-pink-500",
    faqs: [
      {
        question: "What YouTube metrics should I focus on for growth?",
        answer: "The most important YouTube metrics for growth are: Watch Time (total minutes watched), Average View Duration (how long people watch), Click-Through Rate (CTR) on thumbnails, Audience Retention (when people drop off), Subscriber Growth Rate, and Engagement Rate (likes, comments, shares per view)."
      },
      {
        question: "How do I improve my YouTube Analytics?",
        answer: "To improve your YouTube Analytics: analyze your best-performing videos to understand what works, identify drop-off points in audience retention graphs, test different thumbnail styles, optimize your titles for better CTR, create content based on your audience's interests, and use end screens to promote other videos."
      },
      {
        question: "What is a good click-through rate (CTR) for YouTube?",
        answer: "A good YouTube CTR varies by channel size and niche, but generally: 2-10% is average for most channels, 4-6% is considered good, and above 8% is excellent. New channels often see higher CTRs initially. Focus on improving your thumbnails and titles to increase CTR."
      },
      {
        question: "How often should I check my YouTube Analytics?",
        answer: "Check your YouTube Analytics weekly for regular insights and monthly for deeper analysis. Daily checking can lead to over-optimization based on short-term fluctuations. Focus on trends over time rather than day-to-day variations."
      }
    ]
  },
  {
    icon: Users,
    title: "Audience & Engagement",
    color: "from-blue-400 to-purple-500",
    faqs: [
      {
        question: "How do I increase engagement on my YouTube videos?",
        answer: "To increase engagement: ask questions in your videos and encourage comments, respond to comments quickly, create community posts, use polls and stories, add clear calls-to-action, create content that sparks discussion, and build a community around your channel's theme."
      },
      {
        question: "What's the best time to post on YouTube?",
        answer: "The best posting time depends on your audience's location and viewing habits. Generally, 2-4 PM and 6-9 PM in your audience's timezone work well. Use YouTube Analytics to see when your audience is most active and experiment with different posting times."
      },
      {
        question: "How do I find my target audience on YouTube?",
        answer: "Find your target audience by: analyzing your current viewers in YouTube Analytics, researching competitors' audiences, creating detailed viewer personas, using keyword research to understand search intent, engaging with communities in your niche, and testing different content types to see what resonates."
      },
      {
        question: "Should I respond to every comment on my YouTube videos?",
        answer: "While you don't need to respond to every comment, engaging with your audience is crucial for growth. Prioritize responding to questions, meaningful comments, and early commenters. This boosts engagement signals and helps build a loyal community."
      }
    ]
  },
  {
    icon: Zap,
    title: "Content & Optimization",
    color: "from-yellow-400 to-orange-500",
    faqs: [
      {
        question: "How do I optimize my YouTube videos for search?",
        answer: "Optimize your YouTube videos by: researching keywords using tools like YouTube's search suggestions, including keywords in your title naturally, writing detailed descriptions with keywords, using relevant tags, creating custom thumbnails, adding closed captions, and organizing videos into playlists."
      },
      {
        question: "What makes a good YouTube thumbnail?",
        answer: "A good YouTube thumbnail should: be visually striking with bright colors, include faces with clear emotions, have readable text (if any), be consistent with your brand, accurately represent the video content, stand out in search results, and be optimized for mobile viewing."
      },
      {
        question: "How long should my YouTube videos be?",
        answer: "Video length depends on your content type and audience. Generally: tutorials work well at 8-15 minutes, entertainment content at 5-10 minutes, and educational content at 10-20 minutes. Focus on keeping viewers engaged throughout rather than hitting a specific length."
      },
      {
        question: "Should I use YouTube Shorts for channel growth?",
        answer: "Yes, YouTube Shorts can significantly boost channel growth. They help you reach new audiences, increase overall watch time, and can drive traffic to your long-form content. Create Shorts that complement your main content strategy and include calls-to-action to subscribe."
      }
    ]
  }
];

export default function FAQPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-6xl mx-auto">
            {/* Breadcrumbs */}
            <Breadcrumbs
              items={[{ name: 'FAQ', href: '/faq' }]}
              className="mb-8"
            />

            {/* Header */}
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <HelpCircle className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-5xl font-bold text-white">
                  Frequently Asked Questions
                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Get answers to common questions about YouTube growth, analytics, and channel optimization.
                Learn how to grow your YouTube channel effectively with expert insights.
              </p>
            </div>

            {/* FAQ Categories */}
            <div className="space-y-12">
              {faqCategories.map((category, categoryIndex) => {
                const IconComponent = category.icon;
                return (
                  <div key={categoryIndex} className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                    <div className="flex items-center gap-4 mb-8">
                      <div className={`w-12 h-12 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h2 className="text-3xl font-bold text-white">{category.title}</h2>
                    </div>

                    <div className="space-y-4">
                      {category.faqs.map((faq, faqIndex) => (
                        <FAQClient key={faqIndex} question={faq.question} answer={faq.answer} />
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* CTA Section */}
            <div className="mt-20 text-center">
              <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
                <h2 className="text-3xl font-bold text-white mb-4">
                  Still Have Questions?
                </h2>
                <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
                  Get detailed analytics and personalized insights for your YouTube channel.
                  Our comprehensive reports answer questions specific to your content and audience.
                </p>
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Analyze Your Channel
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
