"use client";
import { auth, googleProvider } from "@/lib/firebase/client";
import { signInWithPopup } from "firebase/auth";
import { motion } from "framer-motion";
import { Youtube } from "lucide-react";

export default function LoginPage() {
  const onGoogle = async () => {
    await signInWithPopup(auth, googleProvider);
    window.location.href = "/dashboard";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-6">
      <div className="w-full max-w-md bg-white/10 backdrop-blur-sm border border-white/20 rounded-3xl p-8 text-center">
        <div className="flex items-center justify-center gap-2 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
            <Youtube className="w-6 h-6 text-white" />
          </div>
          <span className="text-2xl font-bold text-white">ytuber</span>
        </div>
        <h1 className="text-xl text-white font-semibold mb-2">Sign in</h1>
        <p className="text-gray-300 text-sm mb-6">Use Google to continue</p>
        <motion.button
          onClick={onGoogle}
          className="w-full py-3 rounded-2xl bg-white text-black font-semibold"
          whileHover={{ scale: 1.02, y: -2 }}
          whileTap={{ scale: 0.98 }}
        >
          Continue with Google
        </motion.button>
      </div>
    </div>
  );
}



