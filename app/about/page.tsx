import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'About Us - YTuber',
  description: 'Learn about YTuber - AI-powered YouTube channel analytics and growth insights.',
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">About YTuber</h1>
          
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-white mb-4">Our Mission</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              YTuber is dedicated to empowering YouTube creators with professional-grade analytics and growth insights.
              We believe every creator deserves access to advanced tools to grow their channel
              and understand their audience better.
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">What We Do</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              Our platform combines real YouTube Analytics data with our proprietary analysis systems
              to provide comprehensive channel growth insights. We offer detailed analysis of content performance,
              audience engagement, and growth opportunities.
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">Our Features</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Real YouTube Analytics API integration</li>
              <li>• Advanced content analysis and recommendations</li>
              <li>• YouTube Algorithm compatibility scoring</li>
              <li>• Memory system for tracking growth over time</li>
              <li>• Comprehensive reporting with actionable insights</li>
            </ul>

            <h2 className="text-2xl font-semibold text-white mb-4">Why Choose YTuber?</h2>
            <p className="text-gray-300 leading-relaxed">
              Unlike other analytics tools, YTuber provides not just data, but actionable growth strategies.
              Our system analyzes your content, thumbnails, and performance metrics to give you specific
              recommendations for YouTube growth. With our memory system, you can track your progress over time
              and see how your improvements impact your channel&apos;s performance.
            </p>
          </div>
          
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300"
            >
              Get Started Today
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
