import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { BarChart3, TrendingUp, Users, Eye, Clock, Target, ArrowRight, CheckCircle } from 'lucide-react';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

export const metadata: Metadata = {
  title: 'YouTube Analytics Tools | Advanced Channel Analytics - YTuber',
  description: 'Get comprehensive YouTube analytics and insights. Track performance, understand your audience, and optimize your content strategy with advanced analytics tools.',
  keywords: 'YouTube analytics, YouTube analytics tools, YouTube channel analytics, YouTube performance tracking, YouTube insights, YouTube metrics',
};

const analyticsFeatures = [
  {
    icon: BarChart3,
    title: "Performance Analytics",
    description: "Track views, watch time, engagement rates, and revenue across all your videos with detailed performance breakdowns.",
    metrics: ["View count trends", "Watch time analysis", "Engagement tracking", "Revenue insights"]
  },
  {
    icon: Users,
    title: "Audience Analytics",
    description: "Understand your audience demographics, behavior patterns, and preferences to create better targeted content.",
    metrics: ["Demographics breakdown", "Geographic distribution", "Device usage", "Viewing patterns"]
  },
  {
    icon: TrendingUp,
    title: "Growth Analytics",
    description: "Monitor your channel's growth trajectory with subscriber trends, view velocity, and growth rate analysis.",
    metrics: ["Subscriber growth", "View velocity", "Growth rate trends", "Milestone tracking"]
  },
  {
    icon: Target,
    title: "Content Analytics",
    description: "Analyze individual video performance, identify top-performing content, and discover optimization opportunities.",
    metrics: ["Video performance", "Content optimization", "Thumbnail analysis", "Title effectiveness"]
  }
];

const keyMetrics = [
  {
    icon: Eye,
    title: "Views & Impressions",
    description: "Track how many people see and click on your content",
    importance: "Essential for understanding reach and visibility"
  },
  {
    icon: Clock,
    title: "Watch Time & Retention",
    description: "Monitor how long viewers stay engaged with your videos",
    importance: "Critical for YouTube algorithm ranking"
  },
  {
    icon: Users,
    title: "Subscriber Growth",
    description: "Track your audience growth and subscriber acquisition",
    importance: "Key indicator of channel health and growth"
  },
  {
    icon: TrendingUp,
    title: "Engagement Rate",
    description: "Measure likes, comments, shares, and overall interaction",
    importance: "Shows content quality and audience connection"
  }
];

export default function YouTubeAnalyticsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumbs */}
          <Breadcrumbs 
            items={[{ name: 'YouTube Analytics', href: '/youtube-analytics' }]}
            className="mb-8"
          />

          {/* Hero Section */}
          <div className="text-center mb-20">
            <h1 className="text-6xl font-bold text-white mb-6">
              YouTube Analytics
              <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent"> Made Clear</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              Get comprehensive insights into your YouTube channel&apos;s performance.
              Understand your audience, track growth, and optimize your content strategy with advanced analytics.
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <BarChart3 className="w-5 h-5 mr-2" />
              Analyze Your Channel
            </Link>
          </div>

          {/* Analytics Features */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Comprehensive Analytics Features
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Everything you need to understand and optimize your YouTube channel performance
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {analyticsFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white">{feature.title}</h3>
                    </div>
                    
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      {feature.description}
                    </p>
                    
                    <ul className="space-y-2">
                      {feature.metrics.map((metric, metricIndex) => (
                        <li key={metricIndex} className="flex items-center gap-3 text-gray-300">
                          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                          {metric}
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Key Metrics */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Key YouTube Metrics to Track
              </h2>
              <p className="text-xl text-gray-300">
                Focus on the metrics that matter most for YouTube growth
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {keyMetrics.map((metric, index) => {
                const IconComponent = metric.icon;
                return (
                  <div key={index} className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10 text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white mb-3">{metric.title}</h3>
                    <p className="text-gray-300 text-sm mb-3">{metric.description}</p>
                    <p className="text-blue-400 text-xs font-medium">{metric.importance}</p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Benefits Section */}
          <div className="mb-20">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-12 border border-white/10">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  Why YouTube Analytics Matter
                </h2>
              </div>
              
              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">Data-Driven Decisions</h3>
                  <p className="text-gray-300">Make informed decisions based on real performance data rather than guesswork.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">Optimize Performance</h3>
                  <p className="text-gray-300">Identify what works and what doesn&apos;t to continuously improve your content strategy.</p>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">Understand Your Audience</h3>
                  <p className="text-gray-300">Learn who your viewers are and what content they prefer to watch.</p>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-2xl p-12 border border-white/10">
              <h2 className="text-4xl font-bold text-white mb-6">
                Get Advanced YouTube Analytics Today
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Start understanding your YouTube channel&apos;s performance with comprehensive analytics
                and actionable insights that drive real growth.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Start Analytics Report
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/blog/youtube-analytics-explained"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20"
                >
                  Learn About Analytics
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
