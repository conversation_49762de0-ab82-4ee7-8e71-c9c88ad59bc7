import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://ytuber.life'
  const currentDate = new Date()

  // Blog posts with their actual dates
  const blogPosts = [
    {
      slug: 'how-to-grow-youtube-channel-2025',
      lastModified: new Date('2025-01-15'),
      priority: 0.9
    },
    {
      slug: 'youtube-analytics-explained',
      lastModified: new Date('2025-01-12'),
      priority: 0.8
    },
    {
      slug: 'youtube-algorithm-tips',
      lastModified: new Date('2025-01-10'),
      priority: 0.8
    },
    {
      slug: 'youtube-thumbnail-optimization',
      lastModified: new Date('2025-01-08'),
      priority: 0.7
    },
    {
      slug: 'youtube-growth-tools',
      lastModified: new Date('2025-01-05'),
      priority: 0.8
    },
    {
      slug: 'youtube-content-strategy',
      lastModified: new Date('2025-01-03'),
      priority: 0.7
    },
    {
      slug: 'youtube-algorithm-tips',
      lastModified: new Date('2025-01-10'),
      priority: 0.8
    },
    {
      slug: 'youtube-thumbnail-optimization',
      lastModified: new Date('2025-01-08'),
      priority: 0.7
    }
  ]

  return [
    // Main pages
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/youtube-growth`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/youtube-analytics`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/youtube-growth-tools`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/faq`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/youtube-growth-tips`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/testimonials`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/success-stories`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/reviews`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },

    // Blog posts
    ...blogPosts.map(post => ({
      url: `${baseUrl}/blog/${post.slug}`,
      lastModified: post.lastModified,
      changeFrequency: 'weekly' as const,
      priority: post.priority,
    })),

    // Static pages
    {
      url: `${baseUrl}/about`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/shipping-returns`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.5,
    },
  ]
}
