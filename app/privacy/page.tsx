import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Privacy Policy - YTuber',
  description: 'YTuber privacy policy and data protection information.',
};

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">Privacy Policy</h1>
          
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <p className="text-gray-300 mb-6">
              <strong>Last updated:</strong> {new Date().toLocaleDateString()}
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Information We Collect</h2>
            <h3 className="text-xl font-semibold text-white mb-3">Account Information:</h3>
            <ul className="text-gray-300 space-y-2 mb-4">
              <li>• Email address and name (via Google OAuth)</li>
              <li>• YouTube channel access permissions</li>
              <li>• Payment information (processed securely via Iyzico)</li>
            </ul>
            
            <h3 className="text-xl font-semibold text-white mb-3">YouTube Data:</h3>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Channel analytics and performance metrics</li>
              <li>• Video metadata and statistics</li>
              <li>• Audience demographics and engagement data</li>
              <li>• Content performance history</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">How We Use Your Information</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Generate comprehensive channel analysis reports</li>
              <li>• Provide advanced insights and recommendations</li>
              <li>• Track progress over time (Memory System)</li>
              <li>• Process payments and manage subscriptions</li>
              <li>• Improve our service quality and features</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Data Security</h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              We implement industry-standard security measures to protect your data:
            </p>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Encrypted data transmission (SSL/TLS)</li>
              <li>• Secure cloud storage with access controls</li>
              <li>• Regular security audits and updates</li>
              <li>• Limited access to personal data by authorized personnel only</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Data Sharing</h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              We do not sell or share your personal data with third parties, except:
            </p>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• With your explicit consent</li>
              <li>• To comply with legal obligations</li>
              <li>• With service providers (Google, OpenAI, Iyzico) under strict data protection agreements</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Your Rights</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Access your personal data</li>
              <li>• Request data correction or deletion</li>
              <li>• Withdraw consent for data processing</li>
              <li>• Export your data in a portable format</li>
              <li>• Revoke YouTube access permissions at any time</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Data Retention</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              We retain your data only as long as necessary to provide our services. 
              You can request data deletion at any time, and we will comply within 30 days.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Contact Us</h2>
            <p className="text-gray-300 leading-relaxed">
              If you have any questions about this Privacy Policy or your data, 
              please contact us. We are committed to protecting your privacy and will 
              respond to your inquiries promptly.
            </p>
          </div>
          
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
