import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Shipping & Returns Policy - YTuber',
  description: 'YTuber shipping and returns policy for digital services.',
};

export default function ShippingReturnsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">Shipping & Returns Policy</h1>
          
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-white mb-4">Digital Service Delivery</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              YTuber provides digital analytics services. All reports and analyses are delivered electronically 
              through our platform. There are no physical products to ship.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Service Delivery Timeline</h2>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• <strong>Channel Analysis:</strong> Typically completed within 5-10 minutes</li>
              <li>• <strong>Report Generation:</strong> Available immediately upon completion</li>
              <li>• <strong>PDF/JSON Downloads:</strong> Available instantly after report generation</li>
              <li>• <strong>Memory System Updates:</strong> Processed in real-time for pack users</li>
            </ul>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Refund Policy</h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              Refunds are only provided if analysis fails to generate or technical errors occur.
              We deliver your analysis or provide a refund - no other refund conditions apply.
            </p>

            <h3 className="text-xl font-semibold text-white mb-3">Refund Conditions:</h3>
            <ul className="text-gray-300 space-y-2 mb-6">
              <li>• Refunds only when analysis cannot be generated due to technical failures</li>
              <li>• Refunds only when system errors prevent report delivery</li>
              <li>• Refunds are processed within 5-7 business days</li>
              <li>• No refunds for completed analyses or user dissatisfaction</li>
            </ul>

            <h2 className="text-2xl font-semibold text-white mb-4">Technical Issues</h2>
            <p className="text-gray-300 leading-relaxed mb-6">
              If you experience any technical issues with report generation or access, please contact our
              support team immediately. We will resolve the issue or provide a full refund.
            </p>
            
            <h2 className="text-2xl font-semibold text-white mb-4">Contact for Returns</h2>
            <p className="text-gray-300 leading-relaxed">
              For refund requests or technical issues, please contact us with your order details and 
              the reason for your request. We aim to respond within 24 hours.
            </p>
          </div>
          
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
