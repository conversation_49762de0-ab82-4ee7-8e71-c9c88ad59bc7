import { ImageResponse } from 'next/og'

interface OpenGraphImageProps {
  title: string
  description?: string
  type?: 'default' | 'blog' | 'landing'
}

export default function OpenGraphImage({ 
  title, 
  description = "Grow your YouTube channel with advanced analytics and growth insights",
  type = 'default' 
}: OpenGraphImageProps) {
  return new ImageResponse(
    (
      <div
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#0f172a',
          backgroundImage: 'linear-gradient(135deg, #1e293b 0%, #3730a3 50%, #7c3aed 100%)',
          fontFamily: 'Inter, sans-serif',
        }}
      >
        {/* Logo/Brand */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '40px',
          }}
        >
          <div
            style={{
              width: '60px',
              height: '60px',
              backgroundColor: '#ef4444',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '20px',
            }}
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="white"
            >
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
          </div>
          <div
            style={{
              fontSize: '48px',
              fontWeight: 'bold',
              color: 'white',
            }}
          >
            YTuber
          </div>
        </div>

        {/* Main Title */}
        <div
          style={{
            fontSize: type === 'blog' ? '56px' : '72px',
            fontWeight: 'bold',
            color: 'white',
            textAlign: 'center',
            lineHeight: '1.1',
            marginBottom: '30px',
            maxWidth: '900px',
            padding: '0 40px',
          }}
        >
          {title}
        </div>

        {/* Description */}
        {description && (
          <div
            style={{
              fontSize: '28px',
              color: '#cbd5e1',
              textAlign: 'center',
              maxWidth: '800px',
              padding: '0 40px',
              lineHeight: '1.3',
            }}
          >
            {description}
          </div>
        )}

        {/* Bottom Badge */}
        <div
          style={{
            position: 'absolute',
            bottom: '40px',
            right: '40px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            padding: '12px 24px',
            borderRadius: '25px',
            fontSize: '18px',
            color: 'white',
            fontWeight: '500',
          }}
        >
          ytuber.life
        </div>

        {/* Decorative Elements */}
        <div
          style={{
            position: 'absolute',
            top: '-100px',
            right: '-100px',
            width: '300px',
            height: '300px',
            backgroundColor: '#8b5cf6',
            borderRadius: '50%',
            opacity: 0.3,
            filter: 'blur(60px)',
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '-150px',
            left: '-150px',
            width: '400px',
            height: '400px',
            backgroundColor: '#3b82f6',
            borderRadius: '50%',
            opacity: 0.2,
            filter: 'blur(80px)',
          }}
        />
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  )
}
