interface AuthorMarkupProps {
  name: string;
  bio?: string;
  image?: string;
  url?: string;
  sameAs?: string[];
}

export default function AuthorMarkup({
  name,
  bio = "Expert in YouTube growth strategies and analytics",
  image = "/team/author.jpg",
  url = "https://ytuber.life/about",
  sameAs = [
    "https://twitter.com/ytuber",
    "https://linkedin.com/company/ytuber",
    "https://youtube.com/@ytuber"
  ]
}: AuthorMarkupProps) {
  const authorSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": name,
    "description": bio,
    "image": image.startsWith('http') ? image : `https://ytuber.life${image}`,
    "url": url,
    "sameAs": sameAs,
    "knowsAbout": [
      "YouTube Analytics",
      "YouTube Growth",
      "Content Strategy",
      "Social Media Marketing",
      "Video Marketing"
    ],
    "worksFor": {
      "@type": "Organization",
      "name": "YTuber",
      "url": "https://ytuber.life"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(authorSchema) }}
    />
  );
}
