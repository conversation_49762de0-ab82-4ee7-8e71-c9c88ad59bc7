#!/bin/bash

# YouTube Analytics Platform Deployment Script
# Usage: ./scripts/deploy.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="ytuber"
BUILD_DIR="build"
BACKUP_DIR="backups"

echo "🚀 Starting deployment for $ENVIRONMENT environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment file exists
if [ ! -f ".env.$ENVIRONMENT" ]; then
    log_error "Environment file .env.$ENVIRONMENT not found!"
    exit 1
fi

# Load environment variables
log_info "Loading environment variables..."
export $(cat .env.$ENVIRONMENT | grep -v '^#' | xargs)

# Pre-deployment checks
log_info "Running pre-deployment checks..."

# Check Node.js version
NODE_VERSION=$(node --version)
log_info "Node.js version: $NODE_VERSION"

# Check if required environment variables are set
required_vars=(
    "NEXTAUTH_URL"
    "YOUTUBE_API_KEY"
    "OPENAI_API_KEY"
    "GEMINI_API_KEY"
    "FIREBASE_PROJECT_ID"
    "IYZICO_API_KEY"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        log_error "Required environment variable $var is not set!"
        exit 1
    fi
done

log_success "Pre-deployment checks passed!"

# Create backup
log_info "Creating backup..."
mkdir -p $BACKUP_DIR
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" \
    --exclude=node_modules \
    --exclude=.next \
    --exclude=$BACKUP_DIR \
    --exclude=.git \
    .
log_success "Backup created: $BACKUP_DIR/$BACKUP_NAME.tar.gz"

# Install dependencies
log_info "Installing dependencies..."
npm ci --production=false

# Run tests
log_info "Running tests..."
npm run test:ci || {
    log_error "Tests failed! Deployment aborted."
    exit 1
}

# Type checking
log_info "Running type checks..."
npm run type-check || {
    log_error "Type checking failed! Deployment aborted."
    exit 1
}

# Linting
log_info "Running linter..."
npm run lint || {
    log_warning "Linting issues found, but continuing deployment..."
}

# Database migration (if needed)
if [ "$ENVIRONMENT" = "production" ]; then
    log_info "Running database migrations..."
    npx prisma migrate deploy || {
        log_error "Database migration failed! Deployment aborted."
        exit 1
    }
fi

# Build application
log_info "Building application..."
npm run build || {
    log_error "Build failed! Deployment aborted."
    exit 1
}

# Run security audit
log_info "Running security audit..."
npm audit --audit-level=high || {
    log_warning "Security vulnerabilities found, please review!"
}

# Optimize images (if any)
log_info "Optimizing static assets..."
# Add image optimization commands here if needed

# Deploy based on environment
case $ENVIRONMENT in
    "production")
        log_info "Deploying to production..."
        # Add production deployment commands here
        # Example: Deploy to Vercel, AWS, etc.
        ;;
    "staging")
        log_info "Deploying to staging..."
        # Add staging deployment commands here
        ;;
    *)
        log_error "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

# Post-deployment tasks
log_info "Running post-deployment tasks..."

# Warm up cache
log_info "Warming up cache..."
# Add cache warming commands here

# Health check
log_info "Running health check..."
sleep 10 # Wait for deployment to be ready

HEALTH_URL="$NEXTAUTH_URL/api/admin/health"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")

if [ "$HTTP_STATUS" = "200" ]; then
    log_success "Health check passed!"
else
    log_error "Health check failed! HTTP Status: $HTTP_STATUS"
    exit 1
fi

# Send deployment notification
log_info "Sending deployment notification..."
# Add notification logic here (Slack, Discord, email, etc.)

# Cleanup old backups (keep last 10)
log_info "Cleaning up old backups..."
cd $BACKUP_DIR
ls -t *.tar.gz | tail -n +11 | xargs -r rm
cd ..

log_success "🎉 Deployment completed successfully!"
log_info "Environment: $ENVIRONMENT"
log_info "URL: $NEXTAUTH_URL"
log_info "Backup: $BACKUP_DIR/$BACKUP_NAME.tar.gz"

# Display deployment summary
echo ""
echo "📊 Deployment Summary:"
echo "======================"
echo "Environment: $ENVIRONMENT"
echo "Node.js: $NODE_VERSION"
echo "Build time: $(date)"
echo "Health check: ✅ Passed"
echo "URL: $NEXTAUTH_URL"
echo ""

log_success "Deployment completed! 🚀"
