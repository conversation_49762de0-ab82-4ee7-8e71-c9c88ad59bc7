import { useState, useEffect } from "react";

interface ABTestConfig {
  testId: string;
  userId: string;
  defaultVariant?: string;
}

interface ABTestResult {
  variant: string | null;
  loading: boolean;
  trackConversion: (value?: number, revenue?: number) => Promise<void>;
  trackImpression: () => Promise<void>;
}

export function useABTest({ testId, userId, defaultVariant }: ABTestConfig): ABTestResult {
  const [variant, setVariant] = useState<string | null>(defaultVariant || null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!testId || !userId) {
      setLoading(false);
      return;
    }

    fetchVariant();
  }, [testId, userId]);

  const fetchVariant = async () => {
    try {
      // Mock A/B test variants to avoid API dependency
      const variants = ['variant_a', 'variant_b', 'variant_c'];
      const mockVariant = variants[Math.floor(Math.random() * variants.length)];
      setVariant(mockVariant);
    } catch (error) {
      console.error("Error fetching A/B test variant:", error);
    } finally {
      setLoading(false);
    }
  };

  const trackConversion = async (conversionValue: number = 1, revenue: number = 0) => {
    try {
      // Mock tracking - just log to console
      console.log("A/B Test Conversion:", { testId, userId, conversionValue, revenue });
    } catch (error) {
      console.error("Error tracking conversion:", error);
    }
  };

  const trackImpression = async () => {
    try {
      // Mock tracking - just log to console
      console.log("A/B Test Impression:", { testId, userId });
    } catch (error) {
      console.error("Error tracking impression:", error);
    }
  };

  return {
    variant,
    loading,
    trackConversion,
    trackImpression,
  };
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState({
    pageLoadTime: 0,
    timeToInteractive: 0,
    firstContentfulPaint: 0,
  });

  useEffect(() => {
    // Measure performance metrics
    const measurePerformance = () => {
      if (typeof window !== "undefined" && "performance" in window) {
        const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          setMetrics({
            pageLoadTime: navigation.loadEventEnd - navigation.loadEventStart,
            timeToInteractive: navigation.domInteractive - navigation.fetchStart,
            firstContentfulPaint: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          });
        }
      }
    };

    // Measure after page load
    if (document.readyState === "complete") {
      measurePerformance();
    } else {
      window.addEventListener("load", measurePerformance);
      return () => window.removeEventListener("load", measurePerformance);
    }
  }, []);

  const trackCustomEvent = async (eventName: string, properties: Record<string, any> = {}) => { // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      // Mock tracking - just log to console
      console.log("Custom Event:", eventName, {
        ...properties,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        referrer: document.referrer,
      });
    } catch (error) {
      console.error("Error tracking custom event:", error);
    }
  };

  return {
    metrics,
    trackCustomEvent,
  };
}
