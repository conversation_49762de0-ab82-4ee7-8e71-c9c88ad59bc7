# Google OAuth Setup for YouTube Analytics

## Overview
To enable the YouTube channel analysis feature, you need to set up Google OAuth credentials. This allows the application to access YouTube Analytics data for authenticated users.

## Step-by-Step Setup

### 1. Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `ytuber-4aa8b` (or create a new one if needed)

### 2. Enable Required APIs
1. Go to **APIs & Services** → **Library**
2. Search and enable the following APIs:
   - **YouTube Data API v3**
   - **YouTube Analytics API**
   - **YouTube Reporting API**

### 3. Create OAuth 2.0 Credentials
1. Go to **APIs & Services** → **Credentials**
2. Click **"Create Credentials"** → **"OAuth 2.0 Client ID"**
3. If prompted, configure the OAuth consent screen first:
   - Choose **External** user type
   - Fill in required fields (App name: "YTuber", User support email, etc.)
   - Add your email to test users
   - Add scopes:
     - `https://www.googleapis.com/auth/youtube.readonly`
     - `https://www.googleapis.com/auth/yt-analytics.readonly`

4. Create OAuth Client ID:
   - **Application type**: Web application
   - **Name**: YTuber Analytics
   - **Authorized redirect URIs**:
     - `http://localhost:3000/api/auth/google/callback` (for development)
     - `https://yourdomain.com/api/auth/google/callback` (for production)

### 4. Configure Environment Variables
1. Copy the **Client ID** and **Client Secret** from the credentials page
2. Open `.env.local` in your project root
3. Uncomment and update these lines:
   ```env
   GOOGLE_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=GOCSPX-your-actual-client-secret
   ```

### 5. Test the Setup
1. Restart your development server: `npm run dev`
2. Go to your application and try the "Analyze Now" feature
3. You should be redirected to Google's OAuth consent screen

## Troubleshooting

### Common Issues

**"OAuth not configured" error**
- Make sure `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` are set in `.env.local`
- Restart the development server after adding environment variables

**"Redirect URI mismatch" error**
- Ensure the redirect URI in Google Cloud Console exactly matches: `http://localhost:3000/api/auth/google/callback`
- Check for trailing slashes or typos

**"This app isn't verified" warning**
- This is normal during development
- Click "Advanced" → "Go to YTuber (unsafe)" to continue
- For production, you'll need to verify your app with Google

**"Access blocked" error**
- Make sure you've added the required scopes in the OAuth consent screen
- Ensure your email is added as a test user

### Required Scopes
The application needs these OAuth scopes:
- `openid` - Basic authentication
- `email` - User email access
- `profile` - User profile information
- `https://www.googleapis.com/auth/youtube.readonly` - Read YouTube channel data
- `https://www.googleapis.com/auth/yt-analytics.readonly` - Read YouTube Analytics data

## Security Notes
- Never commit OAuth credentials to version control
- Use different credentials for development and production
- Regularly rotate your client secrets
- Monitor OAuth usage in Google Cloud Console

## Production Deployment
For production deployment:
1. Create separate OAuth credentials for your production domain
2. Update redirect URIs to use your production domain
3. Complete the OAuth app verification process with Google
4. Set production environment variables securely

## Support
If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure all required APIs are enabled in Google Cloud Console
4. Check the OAuth consent screen configuration
