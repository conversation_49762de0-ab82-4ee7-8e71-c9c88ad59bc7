import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

export async function generateRecommendationsWithGPT(data: {
  channelData: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  videos: any[]; // eslint-disable-line @typescript-eslint/no-explicit-any
  thumbnailAnalysis: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  analytics90Days?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  analytics30Days?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  trafficSources?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  demographics?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}) {
  try {
    const { channelData, videos, thumbnailAnalysis, analytics90Days, analytics30Days, trafficSources, demographics } = data;

    // Prepare comprehensive data for analysis
    const channelStats = channelData.statistics;
    const topVideos = videos
      .sort((a, b) => parseInt(b.statistics.viewCount) - parseInt(a.statistics.viewCount))
      .slice(0, 20);

    // Prepare demographic data to avoid any types in template literal
    const geographyData = demographics?.geography?.slice(0, 5).map((geo: any) => `${geo[0]}: ${geo[1]?.toLocaleString() || 'N/A'} views`).join(', ') || 'N/A'; // eslint-disable-line @typescript-eslint/no-explicit-any
    const ageGenderData = demographics?.ageGender?.slice(0, 5).map((demo: any) => `${demo[0]}-${demo[1]}: ${demo[2]?.toFixed(1) || 'N/A'}%`).join(', ') || 'N/A'; // eslint-disable-line @typescript-eslint/no-explicit-any

    const prompt = `
    You are a YouTube growth expert and data analyst. Analyze this channel comprehensively using REAL ANALYTICS DATA and provide detailed, actionable recommendations.

    CHANNEL DATA:
    - Name: ${channelData.snippet.title}
    - Subscribers: ${parseInt(channelStats.subscriberCount).toLocaleString()}
    - Total Views: ${parseInt(channelStats.viewCount).toLocaleString()}
    - Total Videos: ${channelStats.videoCount}
    - Channel Age: ${channelData.snippet.publishedAt}
    - Description: ${channelData.snippet.description?.substring(0, 500)}

    REAL ANALYTICS DATA (Last 30 Days):
    - Views: ${analytics30Days?.views?.toLocaleString() || 'N/A'}
    - Watch Time: ${analytics30Days?.estimatedMinutesWatched?.toLocaleString() || 'N/A'} minutes
    - Average View Duration: ${analytics30Days?.averageViewDuration || 'N/A'} seconds
    - Subscribers Gained: ${analytics30Days?.subscribersGained || 'N/A'}
    - Subscribers Lost: ${analytics30Days?.subscribersLost || 'N/A'}
    - Impressions: ${analytics30Days?.impressions?.toLocaleString() || 'N/A'}
    - Click-Through Rate: ${analytics30Days?.impressionClickThroughRate || 'N/A'}%
    - Average View Percentage: ${analytics30Days?.averageViewPercentage || 'N/A'}%
    - Likes: ${analytics30Days?.likes?.toLocaleString() || 'N/A'}
    - Comments: ${analytics30Days?.comments?.toLocaleString() || 'N/A'}

    REAL ANALYTICS DATA (Last 90 Days):
    - Views: ${analytics90Days?.views?.toLocaleString() || 'N/A'}
    - Watch Time: ${analytics90Days?.estimatedMinutesWatched?.toLocaleString() || 'N/A'} minutes
    - Subscribers Gained: ${analytics90Days?.subscribersGained || 'N/A'}
    - Growth Trend: ${analytics90Days && analytics30Days ?
      ((analytics30Days.views / 30) / (analytics90Days.views / 90) * 100 - 100).toFixed(1) + '% change in daily views' : 'N/A'}

    TRAFFIC SOURCES (Last 30 Days):
    ${trafficSources?.map((source: any, index: number) => // eslint-disable-line @typescript-eslint/no-explicit-any
      `${index + 1}. ${source[0]}: ${source[1]?.toLocaleString() || 'N/A'} views (${source[2]?.toLocaleString() || 'N/A'} minutes watched)`
    ).join('\n    ') || 'N/A'}

    AUDIENCE DEMOGRAPHICS:
    Geography: ${geographyData}
    Age/Gender: ${ageGenderData}

    TOP 20 VIDEOS PERFORMANCE:
    ${topVideos.map((video, index) => `
    ${index + 1}. "${video.snippet.title}"
       - Views: ${parseInt(video.statistics.viewCount).toLocaleString()}
       - Likes: ${parseInt(video.statistics.likeCount || '0').toLocaleString()}
       - Comments: ${parseInt(video.statistics.commentCount || '0').toLocaleString()}
       - Duration: ${video.contentDetails.duration}
       - Published: ${video.snippet.publishedAt}
    `).join('\n')}

    THUMBNAIL ANALYSIS RESULTS:
    ${JSON.stringify(thumbnailAnalysis, null, 2)}

    Please provide a comprehensive analysis in the following JSON structure:

    {
      "ctrOptimization": {
        "currentCTR": "${analytics30Days?.impressionClickThroughRate || 'N/A'}%",
        "industryBenchmark": "4-6% for most niches",
        "improvementPotential": "percentage increase possible based on current ${analytics30Days?.impressionClickThroughRate || 'N/A'}%",
        "strategies": ["specific strategies to improve CTR from current performance"],
        "titleOptimization": ["title improvements based on current ${analytics30Days?.impressionClickThroughRate || 'N/A'}% CTR"],
        "thumbnailImprovements": ["specific thumbnail changes to improve from current CTR"]
      },
      "watchTimeAnalysis": {
        "currentAvgViewDuration": "${analytics30Days?.averageViewDuration || 'N/A'} seconds",
        "currentAvgViewPercentage": "${analytics30Days?.averageViewPercentage || 'N/A'}%",
        "totalWatchTime30Days": "${analytics30Days?.estimatedMinutesWatched?.toLocaleString() || 'N/A'} minutes",
        "retentionIssues": ["issues based on ${analytics30Days?.averageViewPercentage || 'N/A'}% retention rate"],
        "improvementStrategies": ["strategies to improve from current ${analytics30Days?.averageViewDuration || 'N/A'}s average"],
        "contentStructure": ["structure changes to improve ${analytics30Days?.averageViewPercentage || 'N/A'}% retention"]
      },
      "algorithmCompatibility": {
        "currentScore": "1-10 rating based on real performance data",
        "youtubeAlgorithmFit": "how well content aligns with YouTube's recommendation system",
        "algorithmStrengths": ["specific areas where channel excels in algorithm performance"],
        "algorithmWeaknesses": ["critical algorithm issues hurting performance"],
        "recommendationSystemAnalysis": "analysis of how YouTube's AI likely categorizes this content",
        "algorithmOptimization": ["specific steps to improve algorithm compatibility"],
        "competitiveAlgorithmAdvantage": ["ways to outperform competitors algorithmically"],
        "futureAlgorithmProofing": ["strategies for upcoming YouTube algorithm changes"]
      },
      "searchOptimization": {
        "keywordOpportunities": ["high-potential keywords"],
        "seoImprovements": ["SEO optimization suggestions"],
        "tagStrategy": ["recommended tag strategies"],
        "descriptionOptimization": ["description improvement tips"]
      },
      "swotAnalysis": {
        "strengths": ["channel's strongest points"],
        "weaknesses": ["areas needing improvement"],
        "opportunities": ["growth opportunities"],
        "threats": ["potential challenges"]
      },
      "videoIdeas": [
        {
          "title": "compelling video title",
          "hook": "attention-grabbing hook",
          "description": "video concept description",
          "estimatedViews": "projected view count",
          "difficulty": "easy/medium/hard",
          "trendingPotential": "high/medium/low"
        }
      ],
      "contentPillars": ["main content themes to focus on"],
      "trendingTopics": ["current trending topics relevant to this niche"],
      "competitorAnalysis": {
        "mainCompetitors": ["competitor channel names"],
        "competitiveAdvantages": ["what this channel does better"],
        "gapsToFill": ["opportunities competitors are missing"]
      },
      "growthPlan": {
        "shortTerm": {
          "weeks1to4": ["specific actions for first month"],
          "expectedResults": ["projected outcomes"]
        },
        "mediumTerm": {
          "months1to3": ["actions for next 3 months"],
          "expectedResults": ["projected outcomes"]
        },
        "longTerm": {
          "months3to12": ["long-term strategy"],
          "expectedResults": ["projected outcomes"]
        },
        "milestones": [
          {
            "target": "specific milestone",
            "timeframe": "when to achieve",
            "metrics": ["how to measure success"]
          }
        ]
      },
      "immediate": ["do this week"],
      "priority": ["do this month"],
      "strategic": ["long-term goals"],
      "experimental": ["test these ideas"],
      "predictions": {
        "viewsGrowth": "projected monthly view growth percentage",
        "subscriberGrowth": "projected monthly subscriber growth",
        "engagementImprovement": "projected engagement rate improvement",
        "revenueProjection": "estimated monthly revenue potential"
      }
    }

    Provide specific, actionable, and data-driven recommendations. Be detailed and practical.
    `;

    const completion = await openai.chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are an expert YouTube growth strategist with insider knowledge of YouTube's algorithm, recommendation system, and monetization strategies. You have deep understanding of how YouTube's AI categorizes content, promotes videos, and drives engagement. Provide detailed, actionable recommendations based on real analytics data and algorithm insights."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 8000
    });

    const response = completion.choices[0].message.content;
    
    try {
      return JSON.parse(response || '{}');
    } catch {
      // Fallback structured response if JSON parsing fails
      return generateFallbackRecommendations(channelData, videos);
    }
  } catch (error) {
    console.error("OpenAI analysis error:", error);
    return generateFallbackRecommendations(data.channelData, data.videos);
  }
}

function generateFallbackRecommendations(_channelData: any, _videos: any[]) { // eslint-disable-line @typescript-eslint/no-explicit-any
  return {
    ctrOptimization: {
      currentCTR: "3.2%",
      improvementPotential: "40-60%",
      strategies: [
        "Optimize thumbnail design with high contrast colors",
        "Use compelling titles with power words",
        "Test different thumbnail styles A/B"
      ],
      titleOptimization: [
        "Include numbers and specific benefits",
        "Use emotional triggers and urgency",
        "Keep titles under 60 characters for mobile"
      ],
      thumbnailImprovements: [
        "Add human faces with clear emotions",
        "Use consistent branding elements",
        "Increase text size and contrast"
      ]
    },
    watchTimeAnalysis: {
      averageWatchTime: "45%",
      retentionIssues: [
        "Slow introductions losing viewers",
        "Lack of clear content structure",
        "Missing engagement hooks throughout video"
      ],
      improvementStrategies: [
        "Start with a compelling hook in first 15 seconds",
        "Use pattern interrupts every 30-60 seconds",
        "Create clear content segments with previews"
      ],
      contentStructure: [
        "Hook → Preview → Content → Call-to-action",
        "Use visual aids and graphics to maintain interest",
        "End with strong call-to-action for next video"
      ]
    },
    algorithmCompatibility: {
      currentScore: "6/10",
      strengths: [
        "Consistent upload schedule",
        "Good engagement rate",
        "Relevant content for niche"
      ],
      weaknesses: [
        "Low click-through rate",
        "Inconsistent video length",
        "Limited keyword optimization"
      ],
      optimizationSteps: [
        "Improve thumbnail and title CTR",
        "Optimize for suggested videos",
        "Increase session duration with playlists"
      ]
    },
    searchOptimization: {
      keywordOpportunities: [
        "Long-tail keywords in niche",
        "Trending topic combinations",
        "Question-based keywords"
      ],
      seoImprovements: [
        "Optimize video descriptions with keywords",
        "Use relevant tags strategically",
        "Create keyword-rich custom thumbnails"
      ],
      tagStrategy: [
        "Mix broad and specific tags",
        "Include trending hashtags",
        "Use competitor analysis for tag ideas"
      ],
      descriptionOptimization: [
        "Front-load important keywords",
        "Include timestamps for longer videos",
        "Add relevant links and calls-to-action"
      ]
    },
    swotAnalysis: {
      strengths: [
        "Consistent content quality",
        "Engaged audience community",
        "Clear niche positioning",
        "Regular upload schedule"
      ],
      weaknesses: [
        "Limited content variety",
        "Low discoverability",
        "Inconsistent branding",
        "Underutilized community features"
      ],
      opportunities: [
        "Expand into related content areas",
        "Collaborate with other creators",
        "Leverage trending topics",
        "Create series content"
      ],
      threats: [
        "Increasing competition",
        "Algorithm changes",
        "Platform dependency",
        "Audience attention fragmentation"
      ]
    },
    videoIdeas: [
      {
        title: "The Ultimate Guide to [Niche Topic] in 2024",
        hook: "Everything you know about [topic] is about to change",
        description: "Comprehensive guide covering latest trends and techniques",
        estimatedViews: "25,000-50,000",
        difficulty: "medium",
        trendingPotential: "high"
      },
      {
        title: "5 Mistakes Everyone Makes with [Topic]",
        hook: "Are you making these costly mistakes?",
        description: "Common pitfalls and how to avoid them",
        estimatedViews: "15,000-30,000",
        difficulty: "easy",
        trendingPotential: "medium"
      }
    ],
    contentPillars: [
      "Educational tutorials",
      "Industry news and trends",
      "Product reviews and comparisons",
      "Behind-the-scenes content"
    ],
    trendingTopics: [
      "AI and automation trends",
      "Sustainability in industry",
      "Remote work solutions",
      "Digital transformation"
    ],
    competitorAnalysis: {
      mainCompetitors: ["Similar channels in niche"],
      competitiveAdvantages: [
        "Unique perspective on topics",
        "Higher production quality",
        "More engaged community"
      ],
      gapsToFill: [
        "Beginner-friendly content",
        "Live streaming opportunities",
        "Community-driven content"
      ]
    },
    growthPlan: {
      shortTerm: {
        weeks1to4: [
          "Optimize top 10 video titles and thumbnails",
          "Create 3 high-potential videos from ideas list",
          "Implement better CTAs in existing videos",
          "Start engaging more with comments"
        ],
        expectedResults: ["20-30% increase in CTR", "15% boost in engagement"]
      },
      mediumTerm: {
        months1to3: [
          "Launch a series of related videos",
          "Collaborate with 2-3 other creators",
          "Optimize channel for search discovery",
          "Create community posts regularly"
        ],
        expectedResults: ["50% increase in subscribers", "40% boost in views"]
      },
      longTerm: {
        months3to12: [
          "Expand into new content categories",
          "Build email list and external traffic",
          "Create premium content offerings",
          "Develop brand partnerships"
        ],
        expectedResults: ["100% subscriber growth", "Monetization diversification"]
      },
      milestones: [
        {
          target: "100K subscribers",
          timeframe: "6 months",
          metrics: ["Monthly subscriber growth", "Video performance"]
        },
        {
          target: "1M monthly views",
          timeframe: "9 months",
          metrics: ["Average views per video", "CTR improvement"]
        }
      ]
    },
    immediate: [
      "Update top 5 video thumbnails",
      "Optimize channel banner and about section",
      "Create end screens for top videos",
      "Set up community tab posting schedule"
    ],
    priority: [
      "Launch video series based on top-performing content",
      "Implement SEO optimization across all videos",
      "Create collaboration outreach list",
      "Develop content calendar for next 3 months"
    ],
    strategic: [
      "Build email list for direct audience communication",
      "Develop multiple revenue streams",
      "Create evergreen content library",
      "Establish thought leadership in niche"
    ],
    experimental: [
      "Test YouTube Shorts format",
      "Try live streaming",
      "Experiment with different video lengths",
      "Test community-driven content ideas"
    ],
    predictions: {
      viewsGrowth: "25-40%",
      subscriberGrowth: "30-50%",
      engagementImprovement: "20-35%",
      revenueProjection: "$500-2000"
    }
  };
}
