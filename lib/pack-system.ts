// Using Firebase Firestore only - no Prisma needed
console.log('[pack-system] Using Firebase Firestore only');
import { adminDb } from "@/lib/firebase/admin";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler";

export interface PackConfig {
  name: string;
  credits: number;
  price: number;
  hasMemoryAccess: boolean;
  features: string[];
}

export const PACK_CONFIGS: Record<string, PackConfig> = {
  first: {
    name: "First Timer",
    credits: 1,
    price: 2.00,
    hasMemoryAccess: false,
    features: [
      "Real YouTube Analytics Data",
      "AI-Powered Insights (GPT-5 + Gemini 2.5)",
      "YouTube Algorithm Compatibility Score",
      "7 Comprehensive Analysis Categories",
      "PDF + JSON Export"
    ]
  },
  single: {
    name: "Single Report",
    credits: 1,
    price: 4.99,
    hasMemoryAccess: false,
    features: [
      "Real YouTube Analytics Data",
      "AI-Powered Insights (GPT-5 + Gemini 2.5)",
      "YouTube Algorithm Compatibility Score",
      "7 Comprehensive Analysis Categories",
      "PDF + JSON Export"
    ]
  },
  creator: {
    name: "Creator Pack",
    credits: 5,
    price: 19.99,
    hasMemoryAccess: true,
    features: [
      "Everything in Single Report",
      "Memory System - Compare with previous reports",
      "Progress tracking over time",
      "Trend analysis across reports",
      "Priority Support"
    ]
  },
  pro: {
    name: "Pro Pack",
    credits: 10,
    price: 34.99,
    hasMemoryAccess: true,
    features: [
      "Everything in Creator Pack",
      "Advanced Memory System",
      "Multi-channel comparison",
      "Predictive growth modeling",
      "Competitive benchmarking",
      "Premium Support"
    ]
  }
};

export async function getUserPackInfo(userId: string) {
  console.log('[getUserPackInfo] Fetching user pack info from Firestore for user:', userId);

  // Use Firestore directly
  const snap = await adminDb.collection('users').doc(userId).get();

  if (!snap.exists) {
    // Create new user with 3 free credits for testing
    console.log('[getUserPackInfo] Creating new user with 3 free credits');
    const newUserData = {
      currentPack: 'free',
      packCredits: 3, // 3 free credits for testing
      hasMemoryAccess: false,
      isFirstTimer: true,
      createdAt: new Date().toISOString()
    };
    await adminDb.collection('users').doc(userId).set(newUserData);
    return {
      ...newUserData,
      packConfig: null
    };
  }

  const data = snap.data() as any; // eslint-disable-line @typescript-eslint/no-explicit-any
  const currentPack = data.currentPack ?? null;
  const credits = data.packCredits ?? data.credits ?? 0;

  // If existing user has 0 credits, give them 3 free credits for testing
  if (credits === 0) {
    console.log('[getUserPackInfo] Giving existing user 3 free credits for testing');
    await adminDb.collection('users').doc(userId).update({
      packCredits: 3,
      updatedAt: new Date().toISOString()
    });
    return {
      currentPack,
      packCredits: 3,
      hasMemoryAccess: data.hasMemoryAccess ?? (currentPack ? PACK_CONFIGS[currentPack]?.hasMemoryAccess : false),
      isFirstTimer: data.isFirstTimer ?? true,
      createdAt: data.createdAt ?? null,
      packConfig: currentPack ? PACK_CONFIGS[currentPack] : null
    };
  }

  return {
    currentPack,
    packCredits: credits,
    hasMemoryAccess: data.hasMemoryAccess ?? (currentPack ? PACK_CONFIGS[currentPack]?.hasMemoryAccess : false),
    isFirstTimer: data.isFirstTimer ?? true,
    createdAt: data.createdAt ?? null,
    packConfig: currentPack ? PACK_CONFIGS[currentPack] : null
  };
}

export async function consumeCredit(userId: string): Promise<boolean> {
  const operationId = `consume_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log(`[consumeCredit] ${operationId} - Starting credit consumption for user:`, userId);

  try {
    const userRef = adminDb.collection('users').doc(userId);
    const snap = await userRef.get();
    console.log(`[consumeCredit] ${operationId} - User document exists:`, snap.exists);

    const data = snap.exists ? snap.data() as any : null; // eslint-disable-line @typescript-eslint/no-explicit-any
    const currentCredits = data ? (data.packCredits ?? data.credits ?? 0) : 0;

    console.log(`[consumeCredit] ${operationId} - Current credits:`, currentCredits);

    if (currentCredits <= 0) {
      console.log(`[consumeCredit] ${operationId} - Insufficient credits, consumption failed`);
      await ErrorHandler.logError(`Credit consumption failed: insufficient credits (${currentCredits})`, {
        userId,
        endpoint: "pack-system",
        method: "consumeCredit"
      }, "medium");
      return false;
    }

    await userRef.set({ packCredits: currentCredits - 1, updatedAt: new Date().toISOString() }, { merge: true });
    console.log(`[consumeCredit] ${operationId} - Credit consumed successfully, remaining:`, currentCredits - 1);
    return true;
  } catch (e) {
    console.error(`[consumeCredit] ${operationId} - Failed to consume credit:`, (e as Error).message);
    await ErrorHandler.logError(e as Error, {
      userId,
      endpoint: "pack-system",
      method: "consumeCredit"
    }, "high");
    throw new Error('Failed to consume credit');
  }
}

export async function refundCredit(userId: string): Promise<void> {
  const operationId = `refund_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log(`[refundCredit] ${operationId} - Starting credit refund for user:`, userId);

  try {
    const userRef = adminDb.collection('users').doc(userId);
    await adminDb.runTransaction(async (tx) => {
      const snap = await tx.get(userRef);
      console.log(`[refundCredit] ${operationId} - User document exists:`, snap.exists);

      const data = snap.exists ? (snap.data() as any) : {}; // eslint-disable-line @typescript-eslint/no-explicit-any
      const current = data.packCredits ?? data.credits ?? 0;
      console.log(`[refundCredit] ${operationId} - Current credits before refund:`, current);

      tx.set(userRef, { packCredits: current + 1, updatedAt: new Date().toISOString() }, { merge: true });
      console.log(`[refundCredit] ${operationId} - Credit refunded successfully, new balance:`, current + 1);
    });
  } catch (e) {
    console.error(`[refundCredit] ${operationId} - Failed to refund credit:`, (e as Error).message);
    await ErrorHandler.logError(e as Error, {
      userId,
      endpoint: "pack-system",
      method: "refundCredit"
    }, "high");
    throw new Error('Failed to refund credit');
  }
}


export async function addPackToUser(
  userId: string,
  packType: string,
  paymentId?: string
): Promise<void> {
  const operationId = `addpack_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log(`[addPackToUser] ${operationId} - Adding pack to user:`, userId, 'pack:', packType);

  const packConfig = PACK_CONFIGS[packType];
  if (!packConfig) {
    console.error(`[addPackToUser] ${operationId} - Invalid pack type:`, packType);
    await ErrorHandler.logError(`Invalid pack type: ${packType}`, {
      userId,
      endpoint: "pack-system",
      method: "addPackToUser"
    }, "high");
    throw new Error('Invalid pack type');
  }

  console.log(`[addPackToUser] ${operationId} - Pack config:`, {
    credits: packConfig.credits,
    price: packConfig.price,
    hasMemoryAccess: packConfig.hasMemoryAccess
  });

  try {
    // Use Firestore transaction
    const userRef = adminDb.collection('users').doc(userId);
    await adminDb.runTransaction(async (tx) => {
      const snap = await tx.get(userRef);
      console.log(`[addPackToUser] ${operationId} - User document exists:`, snap.exists);

      const data = snap.exists ? (snap.data() as any) : {}; // eslint-disable-line @typescript-eslint/no-explicit-any
      const currentCredits = data.packCredits ?? data.credits ?? 0;
      const newCredits = currentCredits + packConfig.credits;

      console.log(`[addPackToUser] ${operationId} - Credits update:`, {
        current: currentCredits,
        adding: packConfig.credits,
        new: newCredits
      });

      tx.set(userRef, {
        currentPack: packType,
        packCredits: newCredits,
        hasMemoryAccess: packConfig.hasMemoryAccess,
        ...(packType === 'first' ? { isFirstTimer: false } : {}),
        updatedAt: new Date().toISOString()
      }, { merge: true });

      // Record purchase in Firestore
      const purchaseRef = adminDb.collection('purchases').doc();
      console.log(`[addPackToUser] ${operationId} - Recording purchase with ID:`, purchaseRef.id);

      tx.set(purchaseRef, {
        uid: userId,
        packType,
        credits: packConfig.credits,
        amount: packConfig.price,
        status: 'completed',
        paymentId,
        createdAt: new Date().toISOString()
      });
    });

    console.log(`[addPackToUser] ${operationId} - Pack added successfully`);
  } catch (e) {
    console.error(`[addPackToUser] ${operationId} - Failed to add pack:`, (e as Error).message);
    await ErrorHandler.logError(e as Error, {
      userId,
      endpoint: "pack-system",
      method: "addPackToUser"
    }, "high");
    throw new Error('Failed to add pack to user');
  }
}

export async function getPreviousReport(userId: string, channelId: string) {
  console.log('[getPreviousReport] Fetching previous report for user:', userId, 'channel:', channelId);
  if (!channelId) return null;

  try {
    const reportsSnap = await adminDb.collection('reports')
      .where('uid', '==', userId)
      .where('youtubeChannelId', '==', channelId)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (reportsSnap.empty) {
      console.log('[getPreviousReport] No previous report found');
      return null;
    }

    const doc = reportsSnap.docs[0];
    const data = doc.data();
    console.log('[getPreviousReport] Previous report found:', doc.id);

    return {
      id: doc.id,
      data: data.data,
      realAnalyticsData: data.realAnalyticsData,
      createdAt: data.createdAt
    };
  } catch (error) {
    console.error('[getPreviousReport] Error fetching previous report:', (error as Error).message);
    return null;
  }
}

export async function generateMemoryInsights(
  currentData: unknown,
  previousData: unknown
): Promise<{ improvementScore: number; insights: { improvements: Array<{ metric: string; change: string; recommendation: string }>; declines: Array<{ metric: string; change: string; recommendation: string }>; recommendations: string[] } }> {
  // Calculate improvement score based on key metrics
  let improvementScore = 0;
  const insights = {
    improvements: [] as Array<{ metric: string; change: string; recommendation: string }>,
    declines: [] as Array<{ metric: string; change: string; recommendation: string }>,
    recommendations: [] as string[]
  };

  try {
    const current = typeof currentData === 'string' ? JSON.parse(currentData) : currentData as Record<string, unknown>;
    const previous = typeof previousData === 'string' ? JSON.parse(previousData) : previousData as Record<string, unknown>;

    // Compare key metrics
    const metrics = [
      { key: 'views', weight: 0.3 },
      { key: 'impressionClickThroughRate', weight: 0.25 },
      { key: 'averageViewPercentage', weight: 0.25 },
      { key: 'subscribersGained', weight: 0.2 }
    ];

    for (const metric of metrics) {
      const currentValue = (current.analytics as Record<string, unknown>)?.[metric.key] as number || 0;
      const previousValue = (previous.analytics as Record<string, unknown>)?.[metric.key] as number || 0;

      if (previousValue > 0) {
        const change = ((currentValue - previousValue) / previousValue) * 100;
        improvementScore += change * metric.weight;

        if (change > 5) {
          insights.improvements.push({
            metric: metric.key,
            change: change.toFixed(1),
            recommendation: `Great improvement in ${metric.key}! Keep up the current strategy.`
          });
        } else if (change < -5) {
          insights.declines.push({
            metric: metric.key,
            change: change.toFixed(1),
            recommendation: `${metric.key} declined. Consider reviewing your recent content strategy.`
          });
        }
      }
    }

    // Generate specific recommendations based on trends
    if (improvementScore > 10) {
      insights.recommendations.push("Your channel is showing strong growth! Consider increasing your upload frequency to capitalize on this momentum.");
    } else if (improvementScore < -10) {
      insights.recommendations.push("Your metrics show some decline. Focus on improving thumbnail CTR and video retention in your next uploads.");
    } else {
      insights.recommendations.push("Your performance is stable. Try experimenting with new content formats to boost engagement.");
    }

  } catch (error) {
    console.error('Error generating memory insights:', error);
    improvementScore = 0;
  }

  return {
    improvementScore: Math.round(improvementScore * 10) / 10,
    insights
  };
}
