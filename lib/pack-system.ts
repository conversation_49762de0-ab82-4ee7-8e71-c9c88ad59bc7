// Using Firebase Firestore only - no Prisma needed
console.log('[pack-system] Using Firebase Firestore only');
import { adminDb } from "@/lib/firebase/admin";

export interface PackConfig {
  name: string;
  credits: number;
  price: number;
  hasMemoryAccess: boolean;
  features: string[];
}

export const PACK_CONFIGS: Record<string, PackConfig> = {
  first: {
    name: "First Timer",
    credits: 1,
    price: 2.00,
    hasMemoryAccess: false,
    features: [
      "Real YouTube Analytics Data",
      "AI-Powered Insights (GPT-5 + Gemini 2.5)",
      "YouTube Algorithm Compatibility Score",
      "7 Comprehensive Analysis Categories",
      "PDF + JSON Export"
    ]
  },
  single: {
    name: "Single Report",
    credits: 1,
    price: 4.99,
    hasMemoryAccess: false,
    features: [
      "Real YouTube Analytics Data",
      "AI-Powered Insights (GPT-5 + Gemini 2.5)",
      "YouTube Algorithm Compatibility Score",
      "7 Comprehensive Analysis Categories",
      "PDF + JSON Export"
    ]
  },
  creator: {
    name: "Creator Pack",
    credits: 5,
    price: 19.99,
    hasMemoryAccess: true,
    features: [
      "Everything in Single Report",
      "Memory System - Compare with previous reports",
      "Progress tracking over time",
      "Trend analysis across reports",
      "Priority Support"
    ]
  },
  pro: {
    name: "Pro Pack",
    credits: 10,
    price: 34.99,
    hasMemoryAccess: true,
    features: [
      "Everything in Creator Pack",
      "Advanced Memory System",
      "Multi-channel comparison",
      "Predictive growth modeling",
      "Competitive benchmarking",
      "Premium Support"
    ]
  }
};

export async function getUserPackInfo(userId: string) {
  console.log('[getUserPackInfo] Fetching user pack info from Firestore for user:', userId);

  // Use Firestore directly
  const snap = await adminDb.collection('users').doc(userId).get();

  if (!snap.exists) {
    // Create new user with 3 free credits for testing
    console.log('[getUserPackInfo] Creating new user with 3 free credits');
    const newUserData = {
      currentPack: 'free',
      packCredits: 3, // 3 free credits for testing
      hasMemoryAccess: false,
      isFirstTimer: true,
      createdAt: new Date().toISOString()
    };
    await adminDb.collection('users').doc(userId).set(newUserData);
    return {
      ...newUserData,
      packConfig: null
    };
  }

  const data = snap.data() as any; // eslint-disable-line @typescript-eslint/no-explicit-any
  const currentPack = data.currentPack ?? null;
  const credits = data.packCredits ?? data.credits ?? 0;

  // If existing user has 0 credits, give them 3 free credits for testing
  if (credits === 0) {
    console.log('[getUserPackInfo] Giving existing user 3 free credits for testing');
    await adminDb.collection('users').doc(userId).update({
      packCredits: 3,
      updatedAt: new Date().toISOString()
    });
    return {
      currentPack,
      packCredits: 3,
      hasMemoryAccess: data.hasMemoryAccess ?? (currentPack ? PACK_CONFIGS[currentPack]?.hasMemoryAccess : false),
      isFirstTimer: data.isFirstTimer ?? true,
      createdAt: data.createdAt ?? null,
      packConfig: currentPack ? PACK_CONFIGS[currentPack] : null
    };
  }

  return {
    currentPack,
    packCredits: credits,
    hasMemoryAccess: data.hasMemoryAccess ?? (currentPack ? PACK_CONFIGS[currentPack]?.hasMemoryAccess : false),
    isFirstTimer: data.isFirstTimer ?? true,
    createdAt: data.createdAt ?? null,
    packConfig: currentPack ? PACK_CONFIGS[currentPack] : null
  };
}

export async function consumeCredit(userId: string): Promise<boolean> {
  console.log('[consumeCredit] Consuming credit for user:', userId);

  try {
    const userRef = adminDb.collection('users').doc(userId);
    const snap = await userRef.get();
    const data = snap.exists ? snap.data() as any : null; // eslint-disable-line @typescript-eslint/no-explicit-any
    const currentCredits = data ? (data.packCredits ?? data.credits ?? 0) : 0;

    console.log('[consumeCredit] Current credits:', currentCredits);
    if (currentCredits <= 0) return false;

    await userRef.set({ packCredits: currentCredits - 1, updatedAt: new Date().toISOString() }, { merge: true });
    console.log('[consumeCredit] Credit consumed successfully, remaining:', currentCredits - 1);
    return true;
  } catch (e) {
    console.error('[consumeCredit] Failed to consume credit:', (e as Error).message);
    throw new Error('Failed to consume credit');
  }
}

export async function refundCredit(userId: string): Promise<void> {
  console.log('[refundCredit] Refunding credit for user:', userId);

  const userRef = adminDb.collection('users').doc(userId);
  await adminDb.runTransaction(async (tx) => {
    const snap = await tx.get(userRef);
    const data = snap.exists ? (snap.data() as any) : {}; // eslint-disable-line @typescript-eslint/no-explicit-any
    const current = data.packCredits ?? data.credits ?? 0;
    tx.set(userRef, { packCredits: current + 1, updatedAt: new Date().toISOString() }, { merge: true });
    console.log('[refundCredit] Credit refunded successfully, new balance:', current + 1);
  });
}


export async function addPackToUser(
  userId: string,
  packType: string,
  paymentId?: string
): Promise<void> {
  console.log('[addPackToUser] Adding pack to user:', userId, 'pack:', packType);
  const packConfig = PACK_CONFIGS[packType];
  if (!packConfig) {
    throw new Error('Invalid pack type');
  }

  // Use Firestore transaction
  const userRef = adminDb.collection('users').doc(userId);
  await adminDb.runTransaction(async (tx) => {
    const snap = await tx.get(userRef);
    const data = snap.exists ? (snap.data() as any) : {}; // eslint-disable-line @typescript-eslint/no-explicit-any
    const currentCredits = data.packCredits ?? data.credits ?? 0;

    tx.set(userRef, {
      currentPack: packType,
      packCredits: currentCredits + packConfig.credits,
      hasMemoryAccess: packConfig.hasMemoryAccess,
      ...(packType === 'first' ? { isFirstTimer: false } : {}),
      updatedAt: new Date().toISOString()
    }, { merge: true });

    // Record purchase in Firestore
    const purchaseRef = adminDb.collection('purchases').doc();
    tx.set(purchaseRef, {
      uid: userId,
      packType,
      credits: packConfig.credits,
      amount: packConfig.price,
      status: 'completed',
      paymentId,
      createdAt: new Date().toISOString()
    });
  });

  console.log('[addPackToUser] Pack added successfully');
}

export async function getPreviousReport(userId: string, channelId: string) {
  console.log('[getPreviousReport] Fetching previous report for user:', userId, 'channel:', channelId);
  if (!channelId) return null;

  try {
    const reportsSnap = await adminDb.collection('reports')
      .where('uid', '==', userId)
      .where('youtubeChannelId', '==', channelId)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (reportsSnap.empty) {
      console.log('[getPreviousReport] No previous report found');
      return null;
    }

    const doc = reportsSnap.docs[0];
    const data = doc.data();
    console.log('[getPreviousReport] Previous report found:', doc.id);

    return {
      id: doc.id,
      data: data.data,
      realAnalyticsData: data.realAnalyticsData,
      createdAt: data.createdAt
    };
  } catch (error) {
    console.error('[getPreviousReport] Error fetching previous report:', (error as Error).message);
    return null;
  }
}

export async function generateMemoryInsights(
  currentData: unknown,
  previousData: unknown
): Promise<{ improvementScore: number; insights: { improvements: Array<{ metric: string; change: string; recommendation: string }>; declines: Array<{ metric: string; change: string; recommendation: string }>; recommendations: string[] } }> {
  // Calculate improvement score based on key metrics
  let improvementScore = 0;
  const insights = {
    improvements: [] as Array<{ metric: string; change: string; recommendation: string }>,
    declines: [] as Array<{ metric: string; change: string; recommendation: string }>,
    recommendations: [] as string[]
  };

  try {
    const current = typeof currentData === 'string' ? JSON.parse(currentData) : currentData as Record<string, unknown>;
    const previous = typeof previousData === 'string' ? JSON.parse(previousData) : previousData as Record<string, unknown>;

    // Compare key metrics
    const metrics = [
      { key: 'views', weight: 0.3 },
      { key: 'impressionClickThroughRate', weight: 0.25 },
      { key: 'averageViewPercentage', weight: 0.25 },
      { key: 'subscribersGained', weight: 0.2 }
    ];

    for (const metric of metrics) {
      const currentValue = (current.analytics as Record<string, unknown>)?.[metric.key] as number || 0;
      const previousValue = (previous.analytics as Record<string, unknown>)?.[metric.key] as number || 0;

      if (previousValue > 0) {
        const change = ((currentValue - previousValue) / previousValue) * 100;
        improvementScore += change * metric.weight;

        if (change > 5) {
          insights.improvements.push({
            metric: metric.key,
            change: change.toFixed(1),
            recommendation: `Great improvement in ${metric.key}! Keep up the current strategy.`
          });
        } else if (change < -5) {
          insights.declines.push({
            metric: metric.key,
            change: change.toFixed(1),
            recommendation: `${metric.key} declined. Consider reviewing your recent content strategy.`
          });
        }
      }
    }

    // Generate specific recommendations based on trends
    if (improvementScore > 10) {
      insights.recommendations.push("Your channel is showing strong growth! Consider increasing your upload frequency to capitalize on this momentum.");
    } else if (improvementScore < -10) {
      insights.recommendations.push("Your metrics show some decline. Focus on improving thumbnail CTR and video retention in your next uploads.");
    } else {
      insights.recommendations.push("Your performance is stable. Try experimenting with new content formats to boost engagement.");
    }

  } catch (error) {
    console.error('Error generating memory insights:', error);
    improvementScore = 0;
  }

  return {
    improvementScore: Math.round(improvementScore * 10) / 10,
    insights
  };
}
