import { adminDb } from "@/lib/firebase/admin";

export interface ABTest {
  id: string;
  name: string;
  description: string;
  status: "draft" | "running" | "completed" | "paused";
  variants: ABVariant[];
  trafficSplit: number[]; // Percentage for each variant
  startDate: string;
  endDate?: string;
  targetMetric: string; // "conversion_rate" | "ctr" | "revenue_per_user"
  minSampleSize: number;
  confidenceLevel: number; // 95, 99, etc.
  createdAt: string;
  updatedAt: string;
}

export interface ABVariant {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>; // eslint-disable-line @typescript-eslint/no-explicit-any
  metrics: {
    impressions: number;
    conversions: number;
    revenue: number;
    conversionRate: number;
    revenuePerUser: number;
  };
}

export interface ABTestResult {
  testId: string;
  winningVariant: string;
  confidence: number;
  improvement: number;
  isSignificant: boolean;
  recommendation: string;
}

export class ABTestingService {
  
  // Create a new A/B test
  static async createTest(test: Omit<ABTest, "id" | "createdAt" | "updatedAt">): Promise<string> {
    const testData: ABTest = {
      ...test,
      id: `test_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await adminDb.collection("abTests").doc(testData.id).set(testData);
    return testData.id;
  }

  // Get user's variant for a test
  static async getUserVariant(testId: string, userId: string): Promise<string | null> {
    try {
      // Check if test exists and is running
      const testDoc = await adminDb.collection("abTests").doc(testId).get();
      if (!testDoc.exists) return null;
      
      const test = testDoc.data() as ABTest;
      if (test.status !== "running") return null;

      // Check if user already has a variant assigned
      const assignmentDoc = await adminDb
        .collection("abTestAssignments")
        .doc(`${testId}_${userId}`)
        .get();

      if (assignmentDoc.exists) {
        return assignmentDoc.data()?.variantId || null;
      }

      // Assign new variant based on traffic split
      const variantId = this.assignVariant(test.variants, test.trafficSplit);
      
      // Save assignment
      await adminDb
        .collection("abTestAssignments")
        .doc(`${testId}_${userId}`)
        .set({
          testId,
          userId,
          variantId,
          assignedAt: new Date().toISOString()
        });

      return variantId;
    } catch (error) {
      console.error("Error getting user variant:", error);
      return null;
    }
  }

  // Track conversion for A/B test
  static async trackConversion(
    testId: string, 
    userId: string, 
    conversionValue: number = 1,
    revenue: number = 0
  ): Promise<void> {
    try {
      // Get user's variant
      const assignmentDoc = await adminDb
        .collection("abTestAssignments")
        .doc(`${testId}_${userId}`)
        .get();

      if (!assignmentDoc.exists) return;

      const variantId = assignmentDoc.data()?.variantId;
      if (!variantId) return;

      // Update variant metrics
      const variantRef = adminDb
        .collection("abTests")
        .doc(testId);

      await adminDb.runTransaction(async (transaction) => {
        const testDoc = await transaction.get(variantRef);
        if (!testDoc.exists) return;

        const test = testDoc.data() as ABTest;
        const variantIndex = test.variants.findIndex(v => v.id === variantId);
        
        if (variantIndex === -1) return;

        // Update metrics
        test.variants[variantIndex].metrics.conversions += conversionValue;
        test.variants[variantIndex].metrics.revenue += revenue;
        test.variants[variantIndex].metrics.conversionRate = 
          test.variants[variantIndex].metrics.conversions / 
          Math.max(test.variants[variantIndex].metrics.impressions, 1) * 100;
        test.variants[variantIndex].metrics.revenuePerUser = 
          test.variants[variantIndex].metrics.revenue / 
          Math.max(test.variants[variantIndex].metrics.conversions, 1);

        test.updatedAt = new Date().toISOString();

        transaction.update(variantRef, test as any); // eslint-disable-line @typescript-eslint/no-explicit-any
      });
    } catch (error) {
      console.error("Error tracking conversion:", error);
    }
  }

  // Track impression for A/B test
  static async trackImpression(testId: string, userId: string): Promise<void> {
    try {
      const assignmentDoc = await adminDb
        .collection("abTestAssignments")
        .doc(`${testId}_${userId}`)
        .get();

      if (!assignmentDoc.exists) return;

      const variantId = assignmentDoc.data()?.variantId;
      if (!variantId) return;

      const variantRef = adminDb.collection("abTests").doc(testId);

      await adminDb.runTransaction(async (transaction) => {
        const testDoc = await transaction.get(variantRef);
        if (!testDoc.exists) return;

        const test = testDoc.data() as ABTest;
        const variantIndex = test.variants.findIndex(v => v.id === variantId);
        
        if (variantIndex === -1) return;

        test.variants[variantIndex].metrics.impressions += 1;
        test.variants[variantIndex].metrics.conversionRate = 
          test.variants[variantIndex].metrics.conversions / 
          Math.max(test.variants[variantIndex].metrics.impressions, 1) * 100;

        test.updatedAt = new Date().toISOString();

        transaction.update(variantRef, test as any); // eslint-disable-line @typescript-eslint/no-explicit-any
      });
    } catch (error) {
      console.error("Error tracking impression:", error);
    }
  }

  // Analyze test results
  static async analyzeTest(testId: string): Promise<ABTestResult | null> {
    try {
      const testDoc = await adminDb.collection("abTests").doc(testId).get();
      if (!testDoc.exists) return null;

      const test = testDoc.data() as ABTest;
      
      if (test.variants.length < 2) return null;

      // Simple statistical analysis (in production, use proper statistical tests)
      const controlVariant = test.variants[0];
      const testVariant = test.variants[1];

      const controlRate = controlVariant.metrics.conversionRate;
      const testRate = testVariant.metrics.conversionRate;

      const improvement = ((testRate - controlRate) / Math.max(controlRate, 0.01)) * 100;
      
      // Simple significance test (replace with proper statistical test)
      const minSampleSize = test.minSampleSize || 100;
      const hasEnoughData = controlVariant.metrics.impressions >= minSampleSize && 
                           testVariant.metrics.impressions >= minSampleSize;
      
      const isSignificant = hasEnoughData && Math.abs(improvement) > 5; // 5% threshold
      const confidence = isSignificant ? 95 : 0;

      let recommendation = "";
      if (!hasEnoughData) {
        recommendation = "Continue test - need more data for statistical significance";
      } else if (isSignificant && improvement > 0) {
        recommendation = `Implement variant ${testVariant.name} - shows ${improvement.toFixed(1)}% improvement`;
      } else if (isSignificant && improvement < 0) {
        recommendation = `Keep control variant - test variant shows ${Math.abs(improvement).toFixed(1)}% decrease`;
      } else {
        recommendation = "No significant difference - consider testing different variations";
      }

      return {
        testId,
        winningVariant: improvement > 0 ? testVariant.id : controlVariant.id,
        confidence,
        improvement: Math.abs(improvement),
        isSignificant,
        recommendation
      };
    } catch (error) {
      console.error("Error analyzing test:", error);
      return null;
    }
  }

  // Helper method to assign variant based on traffic split
  private static assignVariant(variants: ABVariant[], trafficSplit: number[]): string {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (let i = 0; i < variants.length; i++) {
      cumulative += trafficSplit[i] || 0;
      if (random <= cumulative) {
        return variants[i].id;
      }
    }

    // Fallback to first variant
    return variants[0]?.id || "";
  }

  // Get all active tests
  static async getActiveTests(): Promise<ABTest[]> {
    try {
      const snapshot = await adminDb
        .collection("abTests")
        .where("status", "==", "running")
        .get();

      return snapshot.docs.map(doc => doc.data() as ABTest);
    } catch (error) {
      console.error("Error getting active tests:", error);
      return [];
    }
  }
}
