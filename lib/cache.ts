import { adminDb } from "@/lib/firebase/admin";

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  hits: number;
}

export class MemoryCache<T> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;

    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  set(key: string, data: T, ttl?: number): void {
    // If cache is full, remove least recently used item
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      hits: 0
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update hit count and timestamp for LRU
    entry.hits++;
    entry.timestamp = Date.now();
    
    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private evictLRU(): void {
    let oldestKey = "";
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate()
    };
  }

  private calculateHitRate(): number {
    let totalHits = 0;
    let totalEntries = 0;

    for (const entry of this.cache.values()) {
      totalHits += entry.hits;
      totalEntries++;
    }

    return totalEntries > 0 ? totalHits / totalEntries : 0;
  }
}

// Persistent cache using Firestore
export class PersistentCache {
  private collection: string;
  private memoryCache: MemoryCache<any>; // eslint-disable-line @typescript-eslint/no-explicit-any

  constructor(collection: string = "cache") {
    this.collection = collection;
    this.memoryCache = new MemoryCache(500, 10 * 60 * 1000); // 10 minutes
  }

  async set(key: string, data: any, ttl: number = 60 * 60 * 1000): Promise<void> { // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      const cacheEntry = {
        data,
        timestamp: Date.now(),
        ttl,
        expiresAt: new Date(Date.now() + ttl).toISOString()
      };

      // Store in memory cache
      this.memoryCache.set(key, data, ttl);

      // Store in Firestore
      await adminDb.collection(this.collection).doc(key).set(cacheEntry);
    } catch (error) {
      console.error("Failed to set cache:", error);
    }
  }

  async get(key: string): Promise<any | null> { // eslint-disable-line @typescript-eslint/no-explicit-any
    try {
      // Try memory cache first
      const memoryData = this.memoryCache.get(key);
      if (memoryData !== null) {
        return memoryData;
      }

      // Try Firestore
      const doc = await adminDb.collection(this.collection).doc(key).get();
      
      if (!doc.exists) {
        return null;
      }

      const entry = doc.data();
      if (!entry) {
        return null;
      }

      // Check if expired
      if (Date.now() - entry.timestamp > entry.ttl) {
        await this.delete(key);
        return null;
      }

      // Store in memory cache for faster access
      this.memoryCache.set(key, entry.data, entry.ttl - (Date.now() - entry.timestamp));

      return entry.data;
    } catch (error) {
      console.error("Failed to get cache:", error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      this.memoryCache.delete(key);
      await adminDb.collection(this.collection).doc(key).delete();
    } catch (error) {
      console.error("Failed to delete cache:", error);
    }
  }

  async clear(): Promise<void> {
    try {
      this.memoryCache.clear();
      
      const batch = adminDb.batch();
      const snapshot = await adminDb.collection(this.collection).get();
      
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error("Failed to clear cache:", error);
    }
  }

  // Clean up expired entries
  async cleanup(): Promise<void> {
    try {
      const now = new Date().toISOString();
      const snapshot = await adminDb
        .collection(this.collection)
        .where("expiresAt", "<", now)
        .get();

      const batch = adminDb.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      if (snapshot.docs.length > 0) {
        await batch.commit();
        console.log(`Cleaned up ${snapshot.docs.length} expired cache entries`);
      }
    } catch (error) {
      console.error("Failed to cleanup cache:", error);
    }
  }
}

// Cache instances for different use cases
export const caches = {
  // YouTube API responses (longer TTL)
  youtube: new PersistentCache("youtube_cache"),
  
  // AI analysis results (medium TTL)
  analysis: new PersistentCache("analysis_cache"),
  
  // User sessions (shorter TTL)
  sessions: new MemoryCache(1000, 30 * 60 * 1000), // 30 minutes
  
  // Rate limiting (in-memory only)
  rateLimit: new MemoryCache(10000, 60 * 60 * 1000), // 1 hour
  
  // General purpose cache
  general: new PersistentCache("general_cache")
};

// Cache key generators
export const cacheKeys = {
  youtubeChannel: (channelId: string) => `youtube:channel:${channelId}`,
  youtubeVideos: (channelId: string) => `youtube:videos:${channelId}`,
  youtubeAnalytics: (channelId: string, startDate: string, endDate: string) => 
    `youtube:analytics:${channelId}:${startDate}:${endDate}`,
  
  analysisResult: (channelId: string, type: string) => `analysis:${type}:${channelId}`,
  
  userSession: (userId: string) => `session:${userId}`,
  
  rateLimit: (identifier: string, endpoint: string) => `ratelimit:${endpoint}:${identifier}`
};

// Utility functions
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  cache: MemoryCache<T> | PersistentCache = caches.general,
  ttl?: number
): Promise<T> {
  // Try to get from cache first
  const cached = await cache.get(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch fresh data
  const data = await fetcher();
  
  // Store in cache
  await cache.set(key, data, ttl);
  
  return data;
}

// Background cache warming
export class CacheWarmer {
  private static instance: CacheWarmer;
  private warmingTasks: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): CacheWarmer {
    if (!CacheWarmer.instance) {
      CacheWarmer.instance = new CacheWarmer();
    }
    return CacheWarmer.instance;
  }

  scheduleWarming(
    key: string,
    fetcher: () => Promise<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
    interval: number,
    cache: MemoryCache<any> | PersistentCache = caches.general // eslint-disable-line @typescript-eslint/no-explicit-any
  ): void {
    // Clear existing task if any
    this.cancelWarming(key);

    const task = setInterval(async () => {
      try {
        const data = await fetcher();
        await cache.set(key, data);
        console.log(`Cache warmed for key: ${key}`);
      } catch (error) {
        console.error(`Failed to warm cache for key ${key}:`, error);
      }
    }, interval);

    this.warmingTasks.set(key, task);
  }

  cancelWarming(key: string): void {
    const task = this.warmingTasks.get(key);
    if (task) {
      clearInterval(task);
      this.warmingTasks.delete(key);
    }
  }

  cancelAllWarming(): void {
    for (const [key] of this.warmingTasks) {
      this.cancelWarming(key);
    }
  }
}
