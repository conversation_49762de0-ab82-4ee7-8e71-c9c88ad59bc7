/**
 * TCMB (Türkiye Cumhuriyet Merkez Bankası) FX rate fetcher
 * Fetches USD/TRY rate from TCMB's today.xml endpoint
 */

interface TcmbCacheEntry {
  rate: number;
  timestamp: number;
  expiresAt: number;
}

let cache: TcmbCacheEntry | null = null;
const CACHE_DURATION_MS = 60 * 60 * 1000; // 60 minutes

/**
 * Parse TCMB XML to extract USD ForexSelling rate
 */
function parseTcmbXml(xmlText: string): number {
  try {
    // Find USD currency block
    const usdMatch = xmlText.match(/<Currency[^>]*CrossOrder="1"[^>]*Kod="USD"[^>]*>(.*?)<\/Currency>/s);
    if (!usdMatch) {
      throw new Error('USD currency not found in TCMB XML');
    }

    const usdBlock = usdMatch[1];
    
    // Extract Unit and ForexSelling
    const unitMatch = usdBlock.match(/<Unit>([\d.]+)<\/Unit>/);
    const forexSellingMatch = usdBlock.match(/<ForexSelling>([\d.]+)<\/ForexSelling>/);
    
    if (!unitMatch || !forexSellingMatch) {
      throw new Error('Unit or ForexSelling not found in USD block');
    }

    const unit = parseFloat(unitMatch[1]);
    const forexSelling = parseFloat(forexSellingMatch[1]);
    
    if (isNaN(unit) || isNaN(forexSelling) || unit <= 0) {
      throw new Error('Invalid Unit or ForexSelling values');
    }

    // Normalize rate (ForexSelling / Unit)
    const rate = forexSelling / unit;
    
    console.log(`[TCMB] Parsed USD rate: ${rate} (ForexSelling: ${forexSelling}, Unit: ${unit})`);
    return rate;
    
  } catch (error) {
    console.error('[TCMB] XML parsing error:', error);
    throw new Error(`Failed to parse TCMB XML: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * USD/TRY rate provider (temporary static mode)
 * Not reading from env; use the constant below for quick manual updates.
 */
export const STATIC_USD_TRY_RATE = 1.0; // güncel kuru buradan değiştirin
export async function getUsdTryRateTcmb(): Promise<number> {
  console.log(`[TCMB] Using STATIC rate: ${STATIC_USD_TRY_RATE}`);
  return STATIC_USD_TRY_RATE;
}

/**
 * Convert USD amount to TRY using TCMB rate
 */
export async function convertUsdToTry(usdAmount: number): Promise<{ tryAmount: number; rate: number }> {
  const rate = await getUsdTryRateTcmb();
  const tryAmount = Math.round(usdAmount * rate * 100) / 100; // Round to 2 decimal places
  
  return { tryAmount, rate };
}
