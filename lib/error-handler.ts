import { adminDb } from "@/lib/firebase/admin";

export interface ErrorLog {
  id: string;
  error: string;
  stack?: string;
  context: {
    userId?: string;
    channelId?: string;
    endpoint: string;
    method: string;
    userAgent?: string;
    ip?: string;
  };
  severity: "low" | "medium" | "high" | "critical";
  timestamp: string;
  resolved: boolean;
}

export class ErrorHandler {
  static async logError(
    error: Error | string,
    context: Partial<ErrorLog["context"]>,
    severity: ErrorLog["severity"] = "medium"
  ): Promise<void> {
    try {
      const errorLog: Omit<ErrorLog, "id"> = {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          endpoint: "unknown",
          method: "unknown",
          ...context
        },
        severity,
        timestamp: new Date().toISOString(),
        resolved: false
      };

      // Store in Firestore
      await adminDb.collection("errorLogs").add(errorLog);

      // For critical errors, create an alert
      if (severity === "critical") {
        await adminDb.collection("alerts").add({
          type: "error",
          message: `Critical error: ${errorLog.error}`,
          metadata: errorLog.context,
          timestamp: new Date().toISOString(),
          resolved: false
        });
      }

      // Log to console in development
      if (process.env.NODE_ENV === "development") {
        console.error("Error logged:", errorLog);
      }
    } catch (logError) {
      // Fallback logging if Firestore fails
      console.error("Failed to log error:", logError);
      console.error("Original error:", error);
    }
  }

  static createApiErrorResponse(
    error: Error | string,
    statusCode: number = 500,
    context?: Partial<ErrorLog["context"]>
  ) {
    const errorMessage = error instanceof Error ? error.message : error;
    
    // Log the error
    this.logError(error, {
      ...context,
      endpoint: context?.endpoint || "api",
      method: context?.method || "unknown"
    }, statusCode >= 500 ? "high" : "medium");

    // Return sanitized error response
    return {
      error: this.sanitizeErrorMessage(errorMessage),
      code: statusCode,
      timestamp: new Date().toISOString()
    };
  }

  private static sanitizeErrorMessage(message: string): string {
    // Remove sensitive information from error messages
    const sensitivePatterns = [
      /api[_\s]?key[s]?[:\s=]+[a-zA-Z0-9]+/gi,
      /token[s]?[:\s=]+[a-zA-Z0-9]+/gi,
      /password[s]?[:\s=]+[^\s]+/gi,
      /secret[s]?[:\s=]+[^\s]+/gi
    ];

    let sanitized = message;
    sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, "[REDACTED]");
    });

    return sanitized;
  }

  static async getErrorStats(timeRange: "24h" | "7d" | "30d" = "24h") {
    try {
      const now = new Date();
      let startDate: Date;

      switch (timeRange) {
        case "24h":
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case "7d":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "30d":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }

      const snapshot = await adminDb
        .collection("errorLogs")
        .where("timestamp", ">=", startDate.toISOString())
        .get();

      const errors = snapshot.docs.map(doc => doc.data() as ErrorLog);

      return {
        total: errors.length,
        bySeverity: {
          critical: errors.filter(e => e.severity === "critical").length,
          high: errors.filter(e => e.severity === "high").length,
          medium: errors.filter(e => e.severity === "medium").length,
          low: errors.filter(e => e.severity === "low").length
        },
        byEndpoint: errors.reduce((acc: Record<string, number>, error) => {
          const endpoint = error.context.endpoint || "unknown";
          acc[endpoint] = (acc[endpoint] || 0) + 1;
          return acc;
        }, {}),
        resolved: errors.filter(e => e.resolved).length,
        unresolved: errors.filter(e => !e.resolved).length
      };
    } catch (error) {
      console.error("Failed to get error stats:", error);
      return null;
    }
  }
}

// Utility functions for common error scenarios
export const handleYouTubeAPIError = (error: any, context?: Partial<ErrorLog["context"]>) => { // eslint-disable-line @typescript-eslint/no-explicit-any
  let severity: ErrorLog["severity"] = "medium";
  let message = "YouTube API error";

  if (error?.response?.status === 403) {
    severity = "high";
    message = "YouTube API quota exceeded or access denied";
  } else if (error?.response?.status === 401) {
    severity = "high";
    message = "YouTube API authentication failed";
  } else if (error?.response?.status >= 500) {
    severity = "critical";
    message = "YouTube API server error";
  }

  return ErrorHandler.createApiErrorResponse(message, error?.response?.status || 500, {
    ...context,
    endpoint: "youtube-api"
  });
};

export const handleFirebaseError = (error: any, context?: Partial<ErrorLog["context"]>) => { // eslint-disable-line @typescript-eslint/no-explicit-any
  let severity: ErrorLog["severity"] = "high";
  let message = "Database error";

  if (error?.code === "permission-denied") {
    message = "Database permission denied";
  } else if (error?.code === "unavailable") {
    severity = "critical";
    message = "Database unavailable";
  } else if (error?.code === "deadline-exceeded") {
    message = "Database timeout";
  }

  return ErrorHandler.createApiErrorResponse(message, 500, {
    ...context,
    endpoint: "firebase"
  });
};

export const handlePaymentError = (error: any, context?: Partial<ErrorLog["context"]>) => { // eslint-disable-line @typescript-eslint/no-explicit-any
  const severity: ErrorLog["severity"] = "high";
  const message = `Payment processing error: ${error?.message || "Unknown payment error"}`;

  return ErrorHandler.createApiErrorResponse(message, 400, {
    ...context,
    endpoint: "payment"
  });
};

export const handleAIServiceError = (error: any, service: "openai" | "gemini", context?: Partial<ErrorLog["context"]>) => { // eslint-disable-line @typescript-eslint/no-explicit-any
  let severity: ErrorLog["severity"] = "medium";
  let message = `${service.toUpperCase()} API error`;

  if (error?.response?.status === 429) {
    severity = "high";
    message = `${service.toUpperCase()} rate limit exceeded`;
  } else if (error?.response?.status === 401) {
    severity = "critical";
    message = `${service.toUpperCase()} authentication failed`;
  }

  return ErrorHandler.createApiErrorResponse(message, error?.response?.status || 500, {
    ...context,
    endpoint: `ai-${service}`
  });
};
