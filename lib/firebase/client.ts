"use client";
import { initializeApp, getApps } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Initialize only in the browser
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

if (typeof window !== "undefined" && !getApps().length) {
  initializeApp(firebaseConfig);
}

export const auth = typeof window !== "undefined" ? getAuth() : (undefined as unknown as ReturnType<typeof getAuth>);
export const googleProvider = new GoogleAuthProvider();
export const db = typeof window !== "undefined" ? getFirestore() : (undefined as unknown as ReturnType<typeof getFirestore>);
export const storage = typeof window !== "undefined" ? getStorage() : (undefined as unknown as ReturnType<typeof getStorage>);

export async function getIdTokenOrNull(): Promise<string | null> {
  if (typeof window === "undefined") return null;
  const user = auth.currentUser;
  if (!user) return null;
  try {
    return await user.getIdToken();
  } catch {
    return null;
  }
}


