import { google } from 'googleapis';

const youtube = google.youtube('v3');
const youtubeAnalytics = google.youtubeAnalytics('v2');

export interface YouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnails: {
    default: { url: string };
    medium: { url: string };
    high: { url: string };
  };
  statistics: {
    viewCount: string;
    subscriberCount: string;
    videoCount: string;
  };
  brandingSettings?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

export interface AnalyticsData {
  views: number;
  estimatedMinutesWatched: number;
  averageViewDuration: number;
  subscribersGained: number;
  subscribersLost: number;
  likes: number;
  dislikes: number;
  comments: number;
  shares: number;
  impressions: number;
  impressionClickThroughRate: number;
  averageViewPercentage: number;
}

export class YouTubeAnalyticsService {
  private auth: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  constructor(accessToken: string) {
    this.auth = new google.auth.OAuth2();
    this.auth.setCredentials({ access_token: accessToken });
  }

  async getUserChannels(): Promise<YouTubeChannel[]> {
    try {
      const response = await youtube.channels.list({
        auth: this.auth,
        part: ['snippet', 'statistics', 'brandingSettings'],
        mine: true,
      });

      const items = response.data.items || [];
      return items.map(item => ({
        id: item.id || '',
        title: item.snippet?.title || '',
        description: item.snippet?.description || '',
        thumbnails: {
          default: { url: item.snippet?.thumbnails?.default?.url || '' },
          medium: { url: item.snippet?.thumbnails?.medium?.url || '' },
          high: { url: item.snippet?.thumbnails?.high?.url || '' }
        },
        statistics: {
          viewCount: item.statistics?.viewCount || '0',
          subscriberCount: item.statistics?.subscriberCount || '0',
          videoCount: item.statistics?.videoCount || '0'
        },
        brandingSettings: item.brandingSettings
      }));
    } catch (error) {
      console.error('Error fetching user channels:', error);
      throw new Error('Failed to fetch channels');
    }
  }

  async getChannelAnalytics(
    channelId: string,
    startDate: string,
    endDate: string
  ): Promise<AnalyticsData> {
    try {
      const response = await youtubeAnalytics.reports.query({
        auth: this.auth,
        ids: `channel==${channelId}`,
        startDate,
        endDate,
        metrics: [
          'views',
          'estimatedMinutesWatched',
          'averageViewDuration',
          'subscribersGained',
          'subscribersLost',
          'likes',
          'dislikes',
          'comments',
          'shares',
          'impressions',
          'impressionClickThroughRate',
          'averageViewPercentage'
        ].join(','),
        dimensions: '',
      });

      const rows = response.data.rows?.[0] || [];
      
      return {
        views: rows[0] || 0,
        estimatedMinutesWatched: rows[1] || 0,
        averageViewDuration: rows[2] || 0,
        subscribersGained: rows[3] || 0,
        subscribersLost: rows[4] || 0,
        likes: rows[5] || 0,
        dislikes: rows[6] || 0,
        comments: rows[7] || 0,
        shares: rows[8] || 0,
        impressions: rows[9] || 0,
        impressionClickThroughRate: rows[10] || 0,
        averageViewPercentage: rows[11] || 0,
      };
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw new Error('Failed to fetch analytics data');
    }
  }

  async getTopVideos(channelId: string, maxResults: number = 10) {
    try {
      // First get the uploads playlist
      const channelResponse = await youtube.channels.list({
        auth: this.auth,
        part: ['contentDetails'],
        id: [channelId],
      });

      const uploadsPlaylistId = channelResponse.data.items?.[0]?.contentDetails?.relatedPlaylists?.uploads;
      
      if (!uploadsPlaylistId) {
        throw new Error('No uploads playlist found');
      }

      // Get recent videos
      const playlistResponse = await youtube.playlistItems.list({
        auth: this.auth,
        part: ['contentDetails'],
        playlistId: uploadsPlaylistId,
        maxResults: 50,
      });

      const videoIds = playlistResponse.data.items?.map(item => item.contentDetails?.videoId).filter(Boolean) as string[] || [];

      // Get video details with statistics
      const videosResponse = await youtube.videos.list({
        auth: this.auth,
        part: ['snippet', 'statistics'],
        id: videoIds,
      });

      return videosResponse.data.items || [];
    } catch (error) {
      console.error('Error fetching top videos:', error);
      throw new Error('Failed to fetch top videos');
    }
  }

  async getAudienceRetention(videoId: string) {
    try {
      const response = await youtubeAnalytics.reports.query({
        auth: this.auth,
        ids: `channel==MINE`,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        metrics: 'audienceWatchRatio,relativeRetentionPerformance',
        dimensions: 'elapsedVideoTimeRatio',
        filters: `video==${videoId}`,
        sort: 'elapsedVideoTimeRatio',
      });

      return response.data.rows || [];
    } catch (error) {
      console.error('Error fetching audience retention:', error);
      return [];
    }
  }

  async getTrafficSources(channelId: string, startDate: string, endDate: string) {
    try {
      const response = await youtubeAnalytics.reports.query({
        auth: this.auth,
        ids: `channel==${channelId}`,
        startDate,
        endDate,
        metrics: 'views,estimatedMinutesWatched',
        dimensions: 'insightTrafficSourceType',
        sort: '-views',
      });

      return response.data.rows || [];
    } catch (error) {
      console.error('Error fetching traffic sources:', error);
      return [];
    }
  }

  async getDemographics(channelId: string, startDate: string, endDate: string) {
    try {
      const [ageGenderResponse, geographyResponse] = await Promise.all([
        youtubeAnalytics.reports.query({
          auth: this.auth,
          ids: `channel==${channelId}`,
          startDate,
          endDate,
          metrics: 'viewerPercentage',
          dimensions: 'ageGroup,gender',
          sort: '-viewerPercentage',
        }),
        youtubeAnalytics.reports.query({
          auth: this.auth,
          ids: `channel==${channelId}`,
          startDate,
          endDate,
          metrics: 'views,estimatedMinutesWatched',
          dimensions: 'country',
          sort: '-views',
          maxResults: 10,
        }),
      ]);

      return {
        ageGender: ageGenderResponse.data.rows || [],
        geography: geographyResponse.data.rows || [],
      };
    } catch (error) {
      console.error('Error fetching demographics:', error);
      return { ageGender: [], geography: [] };
    }
  }
}

export function createYouTubeService(accessToken: string) {
  return new YouTubeAnalyticsService(accessToken);
}
