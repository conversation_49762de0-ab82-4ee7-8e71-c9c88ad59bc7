import Iyzipay from "iyzipay";

let client: Iyzipay | null = null;

export function getIyzico() {
  if (client) return client;
  const apiKey = process.env.IYZICO_API_KEY;
  const secretKey = process.env.IYZICO_SECRET_KEY;
  const baseUrl = process.env.IYZICO_BASE_URL ?? "https://api.iyzipay.com";
  if (!apiKey || !secretKey) throw new Error("Iyzico API anahtarları eksik");
  client = new Iyzipay({ apiKey, secretKey, uri: baseUrl });
  return client;
}



