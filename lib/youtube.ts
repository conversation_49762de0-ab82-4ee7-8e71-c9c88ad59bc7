const YT_BASE = "https://www.googleapis.com/youtube/v3";

export type ChannelPreview = {
  channelId: string;
  title: string;
  avatar: string;
  subs: number;
  lastUpload?: string;
};

export async function resolveChannelIdFromUrl(channelUrl: string): Promise<string> {
  // Accepts urls like https://www.youtube.com/@handle or /channel/ID
  const url = new URL(channelUrl);
  const path = url.pathname;
  if (path.startsWith("/channel/")) {
    return path.split("/channel/")[1];
  }
  if (path.startsWith("/@")) {
    // Use channels.list for forUsername is deprecated; handle requires search.list (forbidden)
    // We rely on web resolve by fetching the page and scraping channel id. Keep light and quota-free.
    const res = await fetch(channelUrl, { headers: { "Accept-Language": "en" } });
    const html = await res.text();
    const match = html.match(/"channelId":"(UC[^"]+)"/);
    if (!match) throw new Error("Handle çözümlenemedi");
    return match[1];
  }
  if (path.startsWith("/c/") || path === "/") {
    // Try scrape as fallback
    const res = await fetch(channelUrl, { headers: { "Accept-Language": "en" } });
    const html = await res.text();
    const match = html.match(/"channelId":"(UC[^"]+)"/);
    if (!match) throw new Error("Kanal çözümlenemedi");
    return match[1];
  }
  throw new Error("Geçersiz kanal linki");
}

export async function fetchChannelPreview(channelId: string): Promise<ChannelPreview> {
  const key = process.env.YOUTUBE_API_KEY;
  if (!key) throw new Error("YOUTUBE_API_KEY missing");
  const url = `${YT_BASE}/channels?part=snippet,contentDetails,statistics&id=${channelId}&key=${key}`;
  const res = await fetch(url, { next: { revalidate: 60 } });
  if (!res.ok) throw new Error("YouTube API hatası");
  const data = await res.json();
  const item = data.items?.[0];
  if (!item) throw new Error("Kanal bulunamadı");
  const uploads = item.contentDetails?.relatedPlaylists?.uploads;

  let lastUpload: string | undefined;
  if (uploads) {
    const piUrl = `${YT_BASE}/playlistItems?part=snippet&playlistId=${uploads}&maxResults=1&key=${key}`;
    const pi = await fetch(piUrl, { next: { revalidate: 60 } }).then((r) => r.json());
    const vi = pi.items?.[0]?.snippet;
    lastUpload = vi?.publishedAt;
  }

  return {
    channelId,
    title: item.snippet?.title,
    avatar: item.snippet?.thumbnails?.default?.url,
    subs: Number(item.statistics?.subscriberCount ?? 0),
    lastUpload,
  };
}

export async function fetchChannelData(channelId: string) {
  const key = process.env.YOUTUBE_API_KEY;
  if (!key) throw new Error("YOUTUBE_API_KEY missing");

  const url = `${YT_BASE}/channels?part=snippet,statistics,brandingSettings,contentDetails&id=${channelId}&key=${key}`;
  const res = await fetch(url, { next: { revalidate: 60 } });
  if (!res.ok) throw new Error("YouTube API hatası");

  const data = await res.json();
  return data.items?.[0];
}

export async function fetchChannelVideos(channelId: string, maxResults: number = 50) {
  const key = process.env.YOUTUBE_API_KEY;
  if (!key) throw new Error("YOUTUBE_API_KEY missing");

  // First, get the uploads playlist ID
  const channelData = await fetchChannelData(channelId);
  const uploadsPlaylistId = channelData?.contentDetails?.relatedPlaylists?.uploads;

  if (!uploadsPlaylistId) {
    throw new Error("Uploads playlist not found");
  }

  // Get video IDs from the uploads playlist
  const playlistUrl = `${YT_BASE}/playlistItems?part=contentDetails&playlistId=${uploadsPlaylistId}&maxResults=${maxResults}&key=${key}`;
  const playlistRes = await fetch(playlistUrl, { next: { revalidate: 60 } });
  if (!playlistRes.ok) throw new Error("Playlist fetch failed");

  const playlistData = await playlistRes.json();
  const videoIds = playlistData.items?.map((item: any) => item.contentDetails.videoId).join(','); // eslint-disable-line @typescript-eslint/no-explicit-any

  if (!videoIds) {
    return [];
  }

  // Get detailed video information
  const videosUrl = `${YT_BASE}/videos?part=snippet,statistics,contentDetails&id=${videoIds}&key=${key}`;
  const videosRes = await fetch(videosUrl, { next: { revalidate: 60 } });
  if (!videosRes.ok) throw new Error("Videos fetch failed");

  const videosData = await videosRes.json();
  return videosData.items || [];
}



