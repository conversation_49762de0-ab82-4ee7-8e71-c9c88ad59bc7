import { Metadata } from 'next';

interface SEOMetadataProps {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
}

export function generateSEOMetadata({
  title,
  description,
  keywords = "YouTube growth, YouTube analytics, YouTube channel growth, grow YouTube channel",
  canonical,
  ogImage = "/og-image.jpg",
  ogType = "website",
  twitterCard = "summary_large_image",
  author = "YTuber Team",
  publishedTime,
  modifiedTime,
  section,
  tags = []
}: SEOMetadataProps): Metadata {
  const baseUrl = "https://ytuber.life";
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`;
  
  return {
    title,
    description,
    keywords,
    authors: [{ name: author }],
    creator: author,
    publisher: "YTuber",
    
    // Open Graph
    openGraph: {
      title,
      description,
      type: ogType as "website" | "article",
      url: canonical || baseUrl,
      images: [
        {
          url: fullOgImage,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      locale: "en_US",
      siteName: "YTuber",
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags }),
    },
    
    // Twitter
    twitter: {
      card: twitterCard as "summary" | "summary_large_image",
      title,
      description,
      images: [fullOgImage],
      creator: "@ytuber",
      site: "@ytuber",
    },
    
    // Additional meta tags
    other: {
      ...(canonical && { canonical }),
    },
    
    // Robots
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // Verification (add your verification codes here)
    verification: {
      // google: 'your-google-verification-code',
      // bing: 'your-bing-verification-code',
    },
  };
}

// Predefined metadata for common pages
export const defaultMetadata = generateSEOMetadata({
  title: "YTuber - YouTube Growth Analytics | Grow Your Channel Fast",
  description: "Grow your YouTube channel with advanced analytics and growth insights. Get actionable strategies to boost views, subscribers, and engagement.",
  canonical: "https://ytuber.life",
});

export const blogMetadata = (title: string, description: string, slug: string) => 
  generateSEOMetadata({
    title: `${title} - YTuber Blog`,
    description,
    canonical: `https://ytuber.life/blog/${slug}`,
    ogType: "article",
    section: "YouTube Growth",
    tags: ["YouTube", "Growth", "Analytics", "Content Creation"],
  });

export const landingPageMetadata = (title: string, description: string, path: string) =>
  generateSEOMetadata({
    title: `${title} - YTuber`,
    description,
    canonical: `https://ytuber.life${path}`,
    ogType: "website",
  });

// JSON-LD structured data generators
export const generateBreadcrumbSchema = (items: Array<{name: string, url: string}>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": item.url
  }))
});

export const generateWebPageSchema = (title: string, description: string, url: string) => ({
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": title,
  "description": description,
  "url": url,
  "isPartOf": {
    "@type": "WebSite",
    "name": "YTuber",
    "url": "https://ytuber.life"
  },
  "about": {
    "@type": "Thing",
    "name": "YouTube Growth",
    "description": "YouTube channel growth strategies and analytics"
  },
  "mainEntity": {
    "@type": "Service",
    "name": "YouTube Analytics",
    "description": "Professional YouTube channel analytics and growth insights"
  }
});

export const generateHowToSchema = (name: string, steps: Array<{name: string, text: string}>) => ({
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": name,
  "description": `Learn ${name.toLowerCase()} with step-by-step instructions`,
  "step": steps.map((step, index) => ({
    "@type": "HowToStep",
    "position": index + 1,
    "name": step.name,
    "text": step.text
  }))
});

export const generateVideoObjectSchema = (title: string, description: string, thumbnailUrl: string, uploadDate: string) => ({
  "@context": "https://schema.org",
  "@type": "VideoObject",
  "name": title,
  "description": description,
  "thumbnailUrl": thumbnailUrl,
  "uploadDate": uploadDate,
  "publisher": {
    "@type": "Organization",
    "name": "YTuber",
    "logo": {
      "@type": "ImageObject",
      "url": "https://ytuber.life/logo.png"
    }
  }
});
