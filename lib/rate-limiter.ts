import { adminDb } from "@/lib/firebase/admin";

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (identifier: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitRecord {
  count: number;
  resetTime: number;
  blocked: boolean;
}

export class RateLimiter {
  private config: RateLimitConfig;
  private cache: Map<string, RateLimitRecord> = new Map();

  constructor(config: RateLimitConfig) {
    this.config = {
      keyGenerator: (id) => id,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    };

    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async checkLimit(identifier: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = this.config.keyGenerator!(identifier);
    const now = Date.now();
    
    let record = this.cache.get(key);
    
    // If no record or window has expired, create new record
    if (!record || now >= record.resetTime) {
      record = {
        count: 0,
        resetTime: now + this.config.windowMs,
        blocked: false
      };
    }

    // Check if limit exceeded
    if (record.count >= this.config.maxRequests) {
      record.blocked = true;
      this.cache.set(key, record);
      
      // Log rate limit violation
      await this.logViolation(identifier, record);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime
      };
    }

    // Increment counter
    record.count++;
    record.blocked = false;
    this.cache.set(key, record);

    return {
      allowed: true,
      remaining: this.config.maxRequests - record.count,
      resetTime: record.resetTime
    };
  }

  private async logViolation(identifier: string, record: RateLimitRecord) {
    try {
      await adminDb.collection("rateLimitViolations").add({
        identifier,
        count: record.count,
        limit: this.config.maxRequests,
        windowMs: this.config.windowMs,
        timestamp: new Date().toISOString(),
        resetTime: new Date(record.resetTime).toISOString()
      });
    } catch (error) {
      console.error("Failed to log rate limit violation:", error);
    }
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, record] of this.cache.entries()) {
      if (now >= record.resetTime) {
        this.cache.delete(key);
      }
    }
  }

  // Reset limit for a specific identifier
  reset(identifier: string) {
    const key = this.config.keyGenerator!(identifier);
    this.cache.delete(key);
  }

  // Get current status for identifier
  getStatus(identifier: string): RateLimitRecord | null {
    const key = this.config.keyGenerator!(identifier);
    return this.cache.get(key) || null;
  }
}

// Pre-configured rate limiters for different endpoints
export const rateLimiters = {
  // General API rate limiting
  api: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100 // 100 requests per 15 minutes
  }),

  // URL analysis rate limiting (more restrictive)
  analysis: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10 // 10 analyses per hour
  }),

  // Authentication rate limiting
  auth: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5 // 5 auth attempts per 15 minutes
  }),

  // Payment rate limiting
  payment: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3 // 3 payment attempts per hour
  }),

  // Admin endpoints (very restrictive)
  admin: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 100 // 100 admin requests per hour
  })
};

// Middleware function for Next.js API routes
export function withRateLimit(limiter: RateLimiter) {
  return async function rateLimitMiddleware(req: Request, identifier?: string) {
    // Extract identifier (IP address or user ID)
    const clientId = identifier || getClientIdentifier(req);
    
    const result = await limiter.checkLimit(clientId);
    
    if (!result.allowed) {
      const resetDate = new Date(result.resetTime);
      throw new Error(`Rate limit exceeded. Try again at ${resetDate.toISOString()}`);
    }

    return {
      remaining: result.remaining,
      resetTime: result.resetTime
    };
  };
}

function getClientIdentifier(req: Request): string {
  // Try to get IP address from various headers
  const forwarded = req.headers.get("x-forwarded-for");
  const realIp = req.headers.get("x-real-ip");
  const cfConnectingIp = req.headers.get("cf-connecting-ip");
  
  return forwarded?.split(",")[0] || realIp || cfConnectingIp || "unknown";
}

// Security utilities
export class SecurityUtils {
  // Validate and sanitize channel URL
  static validateChannelUrl(url: string): { valid: boolean; sanitized?: string; error?: string } {
    try {
      const urlObj = new URL(url);
      
      // Check if it's a YouTube URL
      if (!["www.youtube.com", "youtube.com", "m.youtube.com"].includes(urlObj.hostname)) {
        return { valid: false, error: "URL must be from YouTube" };
      }

      // Check if it's a channel URL
      const path = urlObj.pathname;
      const validPatterns = [
        /^\/channel\/[a-zA-Z0-9_-]+$/,
        /^\/c\/[a-zA-Z0-9_-]+$/,
        /^\/user\/[a-zA-Z0-9_-]+$/,
        /^\/@[a-zA-Z0-9_.-]+$/
      ];

      const isValid = validPatterns.some(pattern => pattern.test(path));
      
      if (!isValid) {
        return { valid: false, error: "Invalid YouTube channel URL format" };
      }

      // Return sanitized URL
      return { 
        valid: true, 
        sanitized: `https://www.youtube.com${path}` 
      };
    } catch (error) {
      return { valid: false, error: "Invalid URL format" };
    }
  }

  // Sanitize user input
  static sanitizeInput(input: string, maxLength: number = 1000): string {
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[<>]/g, "") // Remove potential HTML tags
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/data:/gi, ""); // Remove data: protocol
  }

  // Validate email format
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // Check for suspicious patterns
  static detectSuspiciousActivity(req: Request): { suspicious: boolean; reasons: string[] } {
    const reasons: string[] = [];
    const userAgent = req.headers.get("user-agent") || "";
    const referer = req.headers.get("referer") || "";

    // Check for bot-like user agents
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /php/i
    ];
    
    if (botPatterns.some(pattern => pattern.test(userAgent))) {
      reasons.push("Bot-like user agent detected");
    }

    // Check for missing or suspicious referer
    if (!referer && req.method === "POST") {
      reasons.push("Missing referer for POST request");
    }

    // Check for suspicious referer domains
    if (referer) {
      try {
        const refererUrl = new URL(referer);
        const suspiciousDomains = [
          "localhost", "127.0.0.1", "192.168.", "10.0.", "172.16."
        ];
        
        if (suspiciousDomains.some(domain => refererUrl.hostname.includes(domain))) {
          reasons.push("Suspicious referer domain");
        }
      } catch {
        reasons.push("Invalid referer URL");
      }
    }

    return {
      suspicious: reasons.length > 0,
      reasons
    };
  }

  // Generate secure random token
  static generateSecureToken(length: number = 32): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }
}
